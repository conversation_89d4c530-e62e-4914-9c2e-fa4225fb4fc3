package com.hengjian.extend.event;

import com.hengjian.system.domain.vo.BatchUploadFile;
import com.hengjian.system.domain.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 事件 - OSS文件上传
 *
 * <AUTHOR>
 * @date 2023/6/13
 */
public class OSSUploadEvent {

    private MultipartFile file;

    /**
     * 文件名，与InputStream配对
     */
    private String fileName;
    /**
     * 输入流，与MultipartFile任选其一
     */
    private InputStream inputStream;
    private SysOssVo sysOssVo;

    private BatchUploadFile batchUploadFile;

    public OSSUploadEvent(MultipartFile file) {
        this.file = file;
    }

    public OSSUploadEvent(BatchUploadFile files) {
        this.batchUploadFile = files;
    }

    public OSSUploadEvent(InputStream file, String fileName) {
        this.inputStream = file;
        this.fileName = fileName;
    }

    public MultipartFile getFile() {
        return file;
    }

    public SysOssVo getSysOssVo() {
        return sysOssVo;
    }

    public String getFileName() {
        return fileName;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setSysOssVo(SysOssVo sysOssVo) {
        this.sysOssVo = sysOssVo;
    }
}
