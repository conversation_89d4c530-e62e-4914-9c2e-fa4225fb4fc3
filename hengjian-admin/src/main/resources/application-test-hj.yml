--- # 临时文件存储位置 避免临时文件被系统清理报错
spring.servlet.multipart.location: /www/apps/temp

--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  url: http://127.0.0.1:18081/admin
  instance:
    service-host-type: IP
  username: zsmall
  password: zsmall!123
xxl:
  conf:
    mirrorfile: ${HOME:${HOME:${user.home}}}/Code/applogs/xxl-conf/hengjian-distribution/xxl-conf-mirror.properties
    env: test
    admin:
      address: http://************:8008/xxl-conf-admin
    appname: distribution
    access:
      token:
    auth:
      enabled: false
--- # xxl-job 配置
xxl.job:
  # 执行器开关
  enabled: false
  # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
  admin-addresses: http://************:18080/xxl-job-admin
  # 执行器通讯TOKEN：非空时启用
  access-token: xxl-job
  executor:
    # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
    appname: xxl-job-executor
    # 执行器端口号 执行器从9101开始往后写
    port: 19101
    # 执行器注册：默认IP:PORT
    address:
    # 执行器IP：默认自动获取IP
    ip:
    # 执行器运行日志文件存储磁盘路径
    logpath: ./logs/xxl-job
    # 执行器日志文件保存天数：大于3生效
    logretentiondays: 30

amqp:
  provider: raw
aliyun:
  accessId: LTAI5tFA6nbCt3f9xgu2zvHf
  accessKey: ******************************
  resOwnerId: 1105126166119398

--- # 数据源配置
spring:
  rabbitmq:
      addresses: ************
      port: 5672
      username: guest
      password: guest
      virtual-host: /
      publisher-returns: true
      template:
        mandatory: true
      listener:
        simple:
          acknowledge-mode: manual
          concurrency: 10
          auto-startup: true
          max-concurrency: 50
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: **********************************************************************************************************************************************************************************************************
          username: zs-mall-dev
          password: ji3n7iLN7sjmsixt
        # 从库数据源
#        slave:
#          lazy: true
#          type: ${spring.datasource.type}
#          driverClassName: com.mysql.cj.jdbc.Driver
#          url: ***************************************************************************************************************************************************************************************************
#          username:
#          password:
      #        oracle:
      #          type: ${spring.datasource.type}
      #          driverClassName: oracle.jdbc.OracleDriver
      #          url: *************************************
      #          username: ROOT
      #          password: root
      #          hikari:
      #            connectionTestQuery: SELECT 1 FROM DUAL
      #        postgres:
      #          type: ${spring.datasource.type}
      #          driverClassName: org.postgresql.Driver
      #          url: ******************************************************************************************************************************************
      #          username: root
      #          password: root
      #        sqlserver:
      #          type: ${spring.datasource.type}
      #          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #          url: *******************************************************************************************************************
      #          username: SA
      #          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 连接测试query（配置检测连接是否有效）
        connectionTestQuery: SELECT 1
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 10
    # 密码(如没有密码请注释掉)
    # password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${hengjian.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: true
  # 每个邮箱每日最大发送量
  daily-max: 20
  # 每个邮箱每分钟最大发送量
  minute-max: 2
  javax:
    enabled: true
    host: smtp.exmail.qq.com
    port: 465
    # 是否需要用户名密码验证
    auth: true
    # 发送方，遵循RFC-822标准
    from: <EMAIL>
    # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
    user: <EMAIL>
    # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
    pass: DdnAvUjujF9CLoTA
    # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
    starttlsEnable: true
    # 使用SSL安全连接
    sslEnable: true
    # SMTP超时时长，单位毫秒，缺省值不超时
    timeout: 0
    # Socket连接超时值，单位毫秒，缺省值不超时
    connectionTimeout: 0
  send-grid:
    enabled: false
    app-key:
    from: <EMAIL>
    from-name: testv2
  mailchimp:
    enabled: false
    api-key:
    sub-account:
    from:
    from-name:

--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商
# https://wind.kim/doc/start 文档地址 各个厂商可同时使用
sms:
  enabled: true
  supplierType: vonage
  # 是否使用redis进行缓存 默认false
  redis-cache: true
  # 单账号每日最大发送量
  account-max: 20
  # 单账号每分钟最大发送
  minute-max: 2
  # 阿里云 dysmsapi.aliyuncs.com
  alibaba:
    #请求地址 默认为 dysmsapi.aliyuncs.com 如无特殊改变可以不用设置
    requestUrl: dysmsapi.aliyuncs.com
    #阿里云的accessKey
    accessKeyId: xxxxxxx
    #阿里云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
  tencent:
    #请求地址默认为 sms.tencentcloudapi.com 如无特殊改变可不用设置
    requestUrl: sms.tencentcloudapi.com
    #腾讯云的accessKey
    accessKeyId: xxxxxxx
    #腾讯云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
    #短信sdkAppId
    sdkAppId: appid
    #地域信息默认为 ap-guangzhou 如无特殊改变可不用设置
    territory: ap-guangzhou
  # vonage 专用
  vonage:
    apiKey: 546000b6
    apiSecret: okTKKH2ZTJ2rXXmL
    from: Efulfill
  sms57:
    url: http://120.24.247.128:7862/sms
    # 是否使用https
    use-https: false
    # 是否加密密码
    encrypt-password: true
    # 国内SMS57短信配置
    domestic:
      # 账号
      account: 100601
      # 密码
      password: pFDGJe8Ib9cdh8
      # 虚拟接入码
      extno: 10690
      # 最大流量
      maximumFlow: 100
      # 最大连接数
      maximumConnection: 1
      # 签名
      signature: "【杭州恒健】"
    # 国外SMS57短信配置
    abroad:
      # 账号
      account: xxx
      # 密码
      password: xxx
      # 虚拟接入码
      extno: 1
      # 最大流量
      maximumFlow: 1
      # 签名
      signature: "[HJMall]"


#--- # es
easy-es:
  #默认为true,若为false则认为不启用本框架
  enable: true
  # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  address : ************:19200
  # 默认为http
  schema: http
  #若无 则可省略此行配置
  username: elastic
  #若无 则可省略此行配置
  password: elastic2023!
  # 心跳策略时间 单位:ms
  keep-alive-millis: 18000
  # 连接超时时间 单位:ms
  connectTimeout: 5000
  # 通信超时时间 单位:ms
  socketTimeout: 5000
  # 连接请求超时时间 单位:ms
  connectionRequestTimeout: 5000
  # 最大连接数 单位:个
  maxConnTotal: 100
  # 最大连接路由数 单位:个
  maxConnPerRoute: 100
  # 配置内容 https://www.easy-es.cn/pages/eddebb/
  global-config:
    #索引处理模式,smoothly:平滑模式, not_smoothly:非平滑模式, manual:手动模式
    process-index-mode: SMOOTHLY
    # 开启控制台打印通过本框架生成的DSL语句,默认为开启,测试稳定后的生产环境建议关闭,以提升少量性能
    print-dsl: true
    # 异步处理索引是否阻塞主线程 默认阻塞 数据量过大时调整为非阻塞异步进行 项目启动更快
    asyncProcessIndexBlocking: true
    db-config:
      # 是否开启下划线转驼峰 默认为false
      map-underscore-to-camel-case: true
      # id生成策略 customize为自定义,id值由用户生成,比如取MySQL中的数据id,如缺省此项配置,则id默认策略为es自动生成
      # id-type: customize
      # 字段更新策略 默认为not_null
      field-strategy: ignored
      # 默认开启,查询若指定了size超过1w条时也会自动开启,开启后查询所有匹配数据,若不开启,会导致无法获取数据总条数,其它功能不受影响.
      enable-track-total-hits: true
      # 数据刷新策略,默认为不刷新
      refresh-policy: immediate
      # 索引前缀,可用于区分环境  默认为空 用法和MP的tablePrefix一样的作用和用法
      index-prefix: hj-test_


--- # payment 支付配置
payment:
  payoneer:
    enabled: true
    sandBox: true
    clientKey: ********************************
    clientSecret: wr1dyZl4wSzTpwYz
    programId: 100228510
    redirectUrl: http://************/prod-api/payment/payoneer/payoneerRedirect
    redirectUrlOrder: http://************/prod-api/payV2/payoneerRedirectOrder
    # 发起授权有效时间（单位：秒）
    authorizeEffectiveTime: 120
    lastRedirectUrl: http://************/backend/redirect/resultOuter/payoneer
    # 超时天数校验标准
    additionalTokenExpiredDay: 1
    # 分钟
    paymentExpireOffset: -70

--- # logistics 物流配置
logistics:
  tracking17:
    enabled: true
    key: 33BB0F6263FDAAC40C42681C05D1736D

--- # thirdparty 第三方店铺
thirdparty-shop:
  shopify:
    enabled: true
    clientKey: 309604f0d9f422c1c56008956bb94109
    clientSecret: bc8253e0e4c6ca90496c0801d3da5cb2
    apiVersion: 2023-01
    scope: write_product_listings,read_orders,write_orders,read_products,write_products,read_product_listings,read_third_party_fulfillment_orders,write_third_party_fulfillment_orders,read_shipping,write_shipping,read_translations,write_translations,read_fulfillments,write_fulfillments,read_inventory,write_inventory
    grantOptions:
    redirectUri: https://distribution.ehengjian.com/backend/redirect/authorize
    authorizeUrl: https://%s/admin/oauth/authorize?client_id=%s&grant_options[]=%s&redirect_uri=%s&scope=%s&state=%s
    deleteUrl: https://{shop}/admin/api_permissions/current.json
    fulfillmentServiceName: Fulfillment Service Test
    fulfillmentServiceCallback: https://distribution.ehengjian.com/prod-api/tps/callback/shopify/fulfillment/
  wayfair:
    enabled: true
  amazon:
    enabled: true
    appRedirectUrl: http://localhost:8080/zsmall-api/tps/amazon/toAppAuthorizeUrl
    websiteLoginUrl: http://localhost:8087/redirect?state=%s
    authorizeRedirectUrl: http://localhost:8080/zsmall-api/tps/amazon/authorize/toWebsiteLoginUrl

--- # 谷歌地图API
google:
  maps:
    url: https://maps.googleapis.com/maps/api/distancematrix/json?origins=(origins)&destinations=(destinations)&mode=driving&language=en-us&key=AIzaSyDvaJO722bzSYhzndEcuzr9hvDB5J4kTYU
  geocoding:
    url: https://maps.googleapis.com/maps/api/geocode/json?address=(address)&language=en-us&key=AIzaSyDvaJO722bzSYhzndEcuzr9hvDB5J4kTYU

--- # 文件相关
file:
  # 临时文件存储路径
  temp-save-path: /www/apps/temp

--- # 订单模块所需参数配置
orders:
  # 未支付订单自动取消时间间隔（小时）
  cancelOrdersInterval: 48

--- # PDF生成
pdf:
  tempPath: /www/apps/file/pdf/temp
  templatePath: /www/apps/file/pdf
  fileName: bill-template.ftl
  logoPath: /www/apps/file/pdf/logo-purple-new.png
  fontPath:
    - /www/apps/file/pdf/ping_fang_light.ttf
    - /www/apps/file/pdf/ping_fang_bold.ttf

#rocketmq:
#  # rocketmq地址
#  name-server: **************:19876
#  producer:
#    # 必须填写 group
#    group: zsmall-group
#    send-message-timeout: 10000
distribution:
  airwallex:
    apiKey: 2767bf15e1ffc13fa91656cea1732ecb14228c4f709866b52e3c3d5f59c62d1be67f545eb8f0507011d678b520bd45c0
    clientId: pNTHzk6YSLC04wJHTZWZKg
    webhooksSecretKey: whsec_QzS-4i83CYDduni_wo8N6AEZef9U5z8-
  payoneer:
    userName: MRS_53586814
    token: g2clb0v7grs06m88qsn5mob2l3lbl8q7jdqejt9h
    shopCode: 4587
    redirectUrlOrder: http://************/prod-api/payV2/payoneerRedirectOrder
  lianlian:
    debuggerFlag: 'ON'
    testModel: 'ON'
    merchantId: 20240118001273002
    subMerchantId:
    signType: RSA
    merchantSignKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDchQc5sFC0ULfpIJMGOe4AvSyarIA8qH9EU8G+e3RCLyq1MhTikkw9YTRobA0G0ncWTDqWF2EXnnhS/R+AUz+LZBJL0KGQf3OFAzX972HO5Xm3Wpj/FC+bPoyeldqzXvPBx5DHk+/RKUN4kcBdGz4+hUMmX1Bd7353xDazruMNF/yPy2DKSwg/Kok9Pb+47Ybiv0S+yA6PjW5kccVKDQBnJDrqW0iP6uKd+G9zZx+h76FITKT5S6QyfHhOD5LIrnvSqdH3MNpgey4LQ0UWFUrTbV78Vc9on3pJ7ojTsweDgPcHY/CsK8/C9DZRlo85yD1+hXiKPJP9cc+wW328sckNAgMBAAECggEASpS/RdaI5QMrwC/hKcjr4toYPNO8p0dBi8VcEGVGg5/aLOAcSii6mkPG6s4odbc2yZbMdbjAgLF69m5fehItuqIEwhl0mx9P3ZuhZhnRFZ1s1gNRHWwhUbsM6hG6ntGtVUt+ftFc2XvAMwXwbkzpd1GwzUiNMMfwGOeGrKyoY+OpJJd2fE25JfcAL/lr3Qpirc8rz1jFCW0nfwrQ7mmy7hAbt/WrwjciL5RMISs/+z+CU0vK7KO8it/vUXFBgQYkOlCptXG53X3Y5LJ3cEd8l8I7ABzMeFNBSQNRXOZd4trTFDZ9URIWIvungMlmhYfVWmDbsEfnAGlmXAk07oxnYQKBgQDz/8rzKlP9HGFZbx73S7urIf6Trg7UGkse6L6Z91UNj0176yuHPrn+5uljJ61pobqR0CC6Zm2jD60xxVIKjEimHD1plbJvKNmGhLfHNJjFNx9PAvKhGPZl5veadwUPwzVxARYYQXoUJh6khHGh8cH3j+LhWkyTYojGEWOXedJhFQKBgQDnXZpokZs0z5pZzOCczzP2tTFx0xNtacm1f/JS0hH8hkkjId2spPlajb1iF+ujg/YG4PAY/6h7GBvtzf9dxp4dIYOQj6cyh1bsUjKCzc54A4D/4aS7Ajj+cGDfXjIZaoS39xwPVE5kIlQdS0S+K7ZE+46NDniZDt10sY6Q8jSWGQKBgGYMq8M8Btir0ANpuFfRsvIGS8VUXRtUy+pniaA3k7kSIGQdnZz5HS2BQkeYRq0RWUi29ZlAUcaLPfK9Bm0m8xOWaBEK7RAU07WxOHP2iZaiUVPodbXvDM7d5N0TvmgzStyyiZ0ndXmL3/EK+8Oxq2BxK4D5FWHnKjE9AJxP9S9pAoGBAM5ARGEJxHmcxHVfTbCcii9ZMZ3N09jK4JUbXTN1r7EVKOfXzdyVKPEPwA9NUbPv9IgHSCKQ7mnlgShKZmb7o4JGmRlq5lsGCXgcgojW3UNhpEL3rTlE7vfryH/3YeQEcBHriSklN8jOTXvb2IlLQG5YbqjSiLGxbbwUeQo/0J8pAoGBAJUWiBElSjIM/XUxoX/twtTYfGD+VJeJ//3FMZFSdcbO/wBwUi8ybAns974PRkU6T04qHsyJo0OduCEh7bGtZ/2yY3NFiZVVYu4TgkG3QrYgjVuofsijY9E70sBcyrJfwtAidRCWnjEXiqTU8qtIziErYRFxk8LjX2uD+Pkolj2Z
    llSignKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3sJSJcZaE3D3PB4gpRqXGHtz2nbeXfSl0v9O6Kj7uUE7WCm3B6ThAg/KO1Ndbupi4N/e6YOL53lLwx876trQBE6NvEvUfTJ4T0VI8b7uDZKVZJJHMQy6JvzN8h5yu8L2Lm2evHH6sjD5jQ1ix5lWTrXnulS+OETtFZf2drCVrSKUQb6+ugv0J5Ebjfyla/kS3CyRnR+1/OtpFeg8X3xSWvBUEIA6X/Jw8tC//d1hfky1b0rz1m0J8kcms5jQYUOA0ZqjrTNl+l50BA/M/EAkrEEjOuIvXKG4uExENcQlcsCMf6PxUc+L3nUVmrckK9D6nq9t/BibH7Y7zWhJHg6gNQIDAQAB
    notificationUrl: https://distribution.ehengjian.com/prod-api/distributor/salesChannel/lianlian/checkout/notifications


  tiktok:
    appSecret: "75b67ac9f9a95b822071f149e78e748217fee42e"
    appKey: "6begibkilh2uu"
  shipping:
    address:
      id:
        multi: "1704748687534034946"
        erp: "1704748687534034946"
  boss:
    account:
      authorize:
        redirecturl: "1"

  tenant:
    sales:
      channel:
        tiktok:
          appSecret: "75b67ac9f9a95b822071f149e78e748217fee42e"
          appkey: "6begibkilh2uu"
        microsoft:
          redirecturi: "1"
          scope: "1"
          clientid: "1"
        shein:
          authorizeurl: "1"
          returnurl: "1"
          appid: "1"
        ebay:
          scopes: "1"
          tokenurl: "1"
          authorizeurl: "1"
          runame: "1"
          devid: "1"
          certid: "1"
          appid: "1"
        amz:
          clientsecret: "1"
          clientid: "1"
          appid: "1"
          vendor:
            appid: "1"
        amazonVC:
          appid: "1"
    third:
      flag:
        erp: "test001-01"
    id:
      erp: "DE9D45Z"
  sku:
    isVisibleForSku: true
  specify:
    warehouse:
      id:
        hj: "CA139871"
        bizArk: "CA283055"
  export:
    limit: 500
  open:
    pay:
      async: true
#订单导出附件池目录
orderAttachment:
  ossRootPath: /www/zs-mall/file/orderAttachment
