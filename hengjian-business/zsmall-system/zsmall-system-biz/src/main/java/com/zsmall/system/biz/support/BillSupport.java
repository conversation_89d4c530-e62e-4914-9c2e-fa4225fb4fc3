package com.zsmall.system.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.domain.ProductActivityCheckout;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.domain.ProductActivityPriceItem;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.bill.*;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.productActivity.ActivityCheckoutTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.dto.AddToBillDTO;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 账单相关支持
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillSupport {

    // private static final DateTimeFormatter dateTimeFormatter =
    //     DateTimeFormatter.ofPattern("MMM d, yyyy hh:mm:ss a", Locale.ENGLISH);
    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);

    private final IBillService iBillService;
    private final IBillAbstractService iBillAbstractService;
    private final IBillRelationService iBillRelationService;
    private final IBillErrorRecordService iBillErrorRecordService;
    private final IBillLogService iBillLogService;
    private final IBillAbstractLogService iBillAbstractLogService;
    private final IBillAbstractDetailLogService iBillAbstractDetailLogService;
    private final IBillRelationLogService iBillRelationLogService;
    private final IBillRelationDetailLogService iBillRelationDetailLogService;
    private final IBillAbstractDetailService iBillAbstractDetailService;
    private final IBillRelationDetailService iBillRelationDetailService;

    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IProductActivityService iProductActivityService;
    private final IProductService iProductService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IWholesaleIntentionOrderService iWholesaleIntentionOrderService;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;

    private final BusinessParameterService businessParameterService;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;

    /**
     * 为新的供货商用户生成账单（一次性生成本期和下期）
     * @return
     */
    @InMethodLog("为新的供货商用户生成账单（一次性生成本期和下期）")
    public void generateBillForNewSup(String tenantId) {
//        // 获取最近一次账单结算时间
//        LocalDateTime latestSettlementDate = this.getLatestSettlementDate();
//        // 账单周期编号（不唯一，一般为结算时间的yyyyMMdd）
//        String billCycleNo = LocalDateTimeUtil.format(latestSettlementDate, "yyyyMMdd");
//        log.info("最近一次的账单周期编号 = {}", billCycleNo);
//
//        LocalDateTime latestSettlementCycleBegin = this.getLatestSettlementCycleBegin();
//        LocalDateTime latestSettlementCycleEnd = this.getLatestSettlementCycleEnd();
//
//        Bill queryEntity = new Bill();
//        queryEntity.setTenantId(tenantId);
//        queryEntity.setBillCycleNo(billCycleNo);
//
//        long count = iBillService.countByEntityNotTenant(queryEntity);
//        log.info("存在数量 = {}", count);
//        if (count <= 0) {
//            String billNo = this.generateBillNo(billCycleNo);
//            Bill newBill = new Bill();
//            newBill.setTenantId(tenantId);
//            newBill.setBillNo(billNo);
//            newBill.setBillCycleNo(billCycleNo);
//            newBill.setSettlementDateTime(latestSettlementDate);
//            newBill.setSettlementCycleBegin(latestSettlementCycleBegin);
//            newBill.setSettlementCycleEnd(latestSettlementCycleEnd);
//            newBill.setCurrentIncome(BigDecimal.ZERO);
//            newBill.setCurrentExpenditure(BigDecimal.ZERO);
//            newBill.setCurrentCircularDeposit(BigDecimal.ZERO);
//            newBill.setPreviousCircularDeposit(BigDecimal.ZERO);
//            newBill.setCircularDepositRatio(BigDecimal.valueOf(0.2));
//            newBill.setCurrentTotalAmount(BigDecimal.ZERO);
//            newBill.setBillState(BillStateEnum.Unsettled);
//            newBill.setGenerateState(GenerateStateEnum.NotGenerated);
//            newBill.setWithdrawalState(WithdrawalStateEnum.NotWithdrawal);
//            iBillService.save(newBill);
//
//            List<BillAbstract> billAbstractList = new ArrayList<>();
//            // 生成收入和支出的摘要主表
//            BillAbstract billAbstract_income = new BillAbstract();
//            billAbstract_income.setBillId(newBill.getId());
//            billAbstract_income.setAbstractType(AbstractTypeEnum.Income);
//            billAbstract_income.setAbstractTotalAmount(BigDecimal.ZERO);
//            billAbstractList.add(billAbstract_income);
//
//            BillAbstract billAbstract_expenditure = new BillAbstract();
//            billAbstract_expenditure.setBillId(newBill.getId());
//            billAbstract_expenditure.setAbstractType(AbstractTypeEnum.Expenditure);
//            billAbstract_expenditure.setAbstractTotalAmount(BigDecimal.ZERO);
//            billAbstractList.add(billAbstract_expenditure);
//            iBillAbstractService.saveBatch(billAbstractList);
//
//            // 生成下一期的账单
//            // 获取下一次账单结算日期
//            LocalDateTime nextSettlementDate = this.getNextSettlementDate();
//            // 账单周期编号（不唯一，一般为结算时间的yyyyMMdd）
//            String nextBillCycleNo = LocalDateTimeUtil.format(nextSettlementDate, "yyyyMMdd");
//            log.info("下一次的账单周期编号 = {}", nextBillCycleNo);
//
//            LocalDateTime nextSettlementCycleBegin = this.getSettlementCycleBegin(nextSettlementDate);
//            LocalDateTime nextSettlementCycleEnd = this.getSettlementCycleEnd(nextSettlementDate);
//
//            Bill nextQueryEntity = new Bill();
//            nextQueryEntity.setTenantId(tenantId);
//            nextQueryEntity.setBillCycleNo(nextBillCycleNo);
//            long nextCount = iBillService.countByEntityNotTenant(nextQueryEntity);
//            if (nextCount <= 0) {
//                String nextBillNo = this.generateBillNo(nextBillCycleNo);
//
//                Bill nextBill = new Bill();
//                nextBill.setTenantId(tenantId);
//                nextBill.setBillNo(nextBillNo);
//                nextBill.setBillCycleNo(nextBillCycleNo);
//                nextBill.setSettlementDateTime(nextSettlementDate);
//                nextBill.setSettlementCycleBegin(nextSettlementCycleBegin);
//                nextBill.setSettlementCycleEnd(nextSettlementCycleEnd);
//                nextBill.setCurrentIncome(BigDecimal.ZERO);
//                nextBill.setCurrentExpenditure(BigDecimal.ZERO);
//                nextBill.setCurrentCircularDeposit(BigDecimal.ZERO);
//                nextBill.setPreviousCircularDeposit(BigDecimal.ZERO);
//                nextBill.setCircularDepositRatio(BigDecimal.valueOf(0.2));
//                nextBill.setCurrentTotalAmount(BigDecimal.ZERO);
//                nextBill.setBillState(BillStateEnum.Unsettled);
//                nextBill.setGenerateState(GenerateStateEnum.NotGenerated);
//                nextBill.setWithdrawalState(WithdrawalStateEnum.NotWithdrawal);
//                nextBill.setPreviousBillId(newBill.getId());
//                iBillService.save(nextBill);
//
//                // 生成收入和支出的摘要主表
//                List<BillAbstract> nextBillAbstractList = new ArrayList<>();
//                BillAbstract nextBillAbstract_income = new BillAbstract();
//                nextBillAbstract_income.setBillId(nextBill.getId());
//                nextBillAbstract_income.setAbstractType(AbstractTypeEnum.Income);
//                nextBillAbstract_income.setAbstractTotalAmount(BigDecimal.ZERO);
//                nextBillAbstractList.add(nextBillAbstract_income);
//
//                BillAbstract nextBillAbstract_expenditure = new BillAbstract();
//                nextBillAbstract_expenditure.setBillId(nextBill.getId());
//                nextBillAbstract_expenditure.setAbstractType(AbstractTypeEnum.Expenditure);
//                nextBillAbstract_expenditure.setAbstractTotalAmount(BigDecimal.ZERO);
//                nextBillAbstractList.add(nextBillAbstract_expenditure);
//                iBillAbstractService.saveBatch(nextBillAbstractList);
//            }
//        }
    }

    @InMethodLog("生成下一期账单")
    public void generateNextBill(List<SysTenantVo> tenantList) {
//        // 获取下一次账单结算日期
//        LocalDateTime nextSettlementDate = getNextSettlementDate();
//        // 账单周期编号（不唯一，一般为结算时间的yyyyMMdd）
//        String billCycleNo = LocalDateTimeUtil.format(nextSettlementDate, "yyyyMMdd");
//        log.info("下一次的账单周期编号 = {}", billCycleNo);
//
//        // 结算日期，只会出现1号和15号
//        int dayOfMonth = nextSettlementDate.getDayOfMonth();
//        // 上一周期编号
//        String previousBillCycleNo;
//        if (dayOfMonth == 15) {
//            // 上一个账单结算时间
//            LocalDateTime previousSettlementDate = nextSettlementDate.withDayOfMonth(1);
//            previousBillCycleNo = LocalDateTimeUtil.format(previousSettlementDate, "yyyyMMdd");
//        } else {
//            // 上一个账单结算时间
//            LocalDateTime previousSettlementDate = nextSettlementDate.minusMonths(1).withDayOfMonth(15);
//            previousBillCycleNo = LocalDateTimeUtil.format(previousSettlementDate, "yyyyMMdd");
//        }
//        log.info("上一周期编号 = {}", previousBillCycleNo);
//
//        LocalDateTime nextSettlementCycleBegin = getSettlementCycleBegin(nextSettlementDate);
//        LocalDateTime nextSettlementCycleEnd = getSettlementCycleEnd(nextSettlementDate);
//        log.info("nextSettlementCycleBegin = {} nextSettlementCycleEnd = {}", nextSettlementCycleBegin,
//            nextSettlementCycleEnd);
//
//        List<String> duplicateBillNo = new ArrayList<>();
//        List<Bill> billList = new ArrayList<>();
//        if (CollUtil.isNotEmpty(tenantList)) {
//            for (SysTenantVo sysTenantVo : tenantList) {
//                String tenantId = sysTenantVo.getTenantId();
//
//                Bill queryEntity = new Bill();
//                queryEntity.setTenantId(tenantId);
//                queryEntity.setBillCycleNo(billCycleNo);
//                long count = iBillService.countByEntity(queryEntity);
//                log.info("租户{}，周期编号{}，存在数量 = {}", tenantId, billCycleNo, count);
//                if (count <= 0) {
//                    String billNo = generateBillNo(billCycleNo);
//                    // 防止短时间生成太多导致重复
//                    while (duplicateBillNo.contains(billNo)) {
//                        billNo = generateBillNo(billCycleNo);
//                    }
//                    duplicateBillNo.add(billNo);
//
//                    Bill newBill = new Bill();
//                    newBill.setTenantId(tenantId);
//                    newBill.setBillNo(billNo);
//                    newBill.setBillCycleNo(billCycleNo);
//                    newBill.setSettlementDateTime(nextSettlementDate);
//                    newBill.setSettlementCycleBegin(nextSettlementCycleBegin);
//                    newBill.setSettlementCycleEnd(nextSettlementCycleEnd);
//                    newBill.setCurrentIncome(BigDecimal.ZERO);
//                    newBill.setCurrentExpenditure(BigDecimal.ZERO);
//                    newBill.setCurrentCircularDeposit(BigDecimal.ZERO);
//                    newBill.setPreviousCircularDeposit(BigDecimal.ZERO);
//                    newBill.setCircularDepositRatio(BigDecimal.valueOf(0.2));
//                    newBill.setCurrentTotalAmount(BigDecimal.ZERO);
//                    newBill.setBillState(BillStateEnum.Unsettled);
//                    newBill.setGenerateState(GenerateStateEnum.NotGenerated);
//                    newBill.setWithdrawalState(WithdrawalStateEnum.NotWithdrawal);
//
//                    // 查询上个周期的账单
//                    Bill queryPreviousEntity = new Bill();
//                    queryPreviousEntity.setTenantId(tenantId);
//                    queryPreviousEntity.setBillCycleNo(previousBillCycleNo);
//                    List<Bill> previousBillList = iBillService.queryByEntity(queryPreviousEntity);
//                    if (CollUtil.isNotEmpty(previousBillList)) {
//                        // 上周期账单
//                        Bill previousBill = previousBillList.get(0);
//                        Long previousBillId = previousBill.getId();
//                        BigDecimal currentCircularDeposit = previousBill.getCurrentCircularDeposit();
//
//                        // 设置上一周期账单id
//                        newBill.setPreviousBillId(previousBillId);
//                        // 设置上期循环保证金
//                        newBill.setPreviousCircularDeposit(currentCircularDeposit);
//                        newBill.setCurrentTotalAmount(currentCircularDeposit);
//                    }
//                    billList.add(newBill);
//                }
//            }
//
//            if (CollUtil.isNotEmpty(billList)) {
//                iBillService.saveBatch(billList);
//
//                List<BillAbstract> billAbstractList = new ArrayList<>();
//                // 生成收入和支出的摘要主表
//                for (Bill billEntity : billList) {
//                    BillAbstract billAbstract_income = new BillAbstract();
//                    billAbstract_income.setBillId(billEntity.getId());
//                    billAbstract_income.setAbstractType(AbstractTypeEnum.Income);
//                    billAbstract_income.setAbstractTotalAmount(BigDecimal.ZERO);
//                    billAbstractList.add(billAbstract_income);
//
//                    BillAbstract billAbstract_expenditure = new BillAbstract();
//                    billAbstract_expenditure.setBillId(billEntity.getId());
//                    billAbstract_expenditure.setAbstractType(AbstractTypeEnum.Expenditure);
//                    billAbstract_expenditure.setAbstractTotalAmount(BigDecimal.ZERO);
//                    billAbstractList.add(billAbstract_expenditure);
//                }
//                iBillAbstractService.saveBatch(billAbstractList);
//            }
//        }
    }

    /**
     * 生成新的账单编号
     * @param billCycleNo 账单周期编号（若传入则使用指定周期编号作为开头
     *                    若传null则会调用【获取最近一次账单结算日期】获取最近一次结算日期作为开头）
     * @return
     */
    @InMethodLog("生成新的账单编号")
    public String generateBillNo(String billCycleNo) {
        String prefix = billCycleNo;
        if (StrUtil.isBlank(billCycleNo)) {
            LocalDateTime latestSettlementDate = this.getLatestSettlementDate();
            prefix = LocalDateTimeUtil.format(latestSettlementDate, "yyyyMMdd");
        }

        if (StrUtil.length(prefix) >= 6) {
            prefix = StrUtil.subSuf(prefix, 2);
        }

        String billNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.BillNo, prefix);
        log.info("生成新的账单编号 结果 = {}", billNo);
        return billNo;
    }

    /**
     * 获取最近一次账单结算日期
     * 注意，本方法与【获取最近一次结算周期截止时间】不同的地方在于，本方法是获取结算的时间
     * 如2022年1月15号需要结算，本方法返回的结算日期为2022-01-15
     */
    public LocalDateTime getLatestSettlementDate() {
        DateTime now = DateTime.now();
        log.info("获取最近一次账单结算时间, now = {}", now);
        int dayOfMonth = now.getField(DateField.DAY_OF_MONTH);

        if (dayOfMonth < 15) {
            // 当前日期小于15号的，结算日期为本月15号
            now = now.setField(DateField.DAY_OF_MONTH, 15);
        } else {
            // 当前日期大于15号的，结算日期为下月1号，结算周期截止时间为本月月底23:59:59
            now = DateUtil.beginOfMonth(now.offset(DateField.MONTH, 1));
        }

        now = DateUtil.beginOfDay(now);
        log.info("获取最近一次账单结算时间 = {}", now);
        return DateUtil.toLocalDateTime(now);
    }

    /**
     * 获取下一次账单结算日期
     */
    public LocalDateTime getNextSettlementDate() {
        DateTime now = DateTime.now();
        log.info("获取下一次账单结算日期, now = {}", now);
        int dayOfMonth = now.getField(DateField.DAY_OF_MONTH);

        if (dayOfMonth < 15) {  // 当前日期小于15号，则当前时间处于1号-15号的结算周期内，所以下一个结算周期为下月1号
            now = DateUtil.beginOfMonth(now.offset(DateField.MONTH, 1));
        } else {  // 当前日期大于/等于15号，则当前时间处于15号-月底的结算周期内，所以下一个结算周期为下月15号
            now.offset(DateField.MONTH, 1).setField(DateField.DAY_OF_MONTH, 15);
        }

        now = DateUtil.beginOfDay(now);
        log.info("获取下一次账单结算日期 = {}", now);
        return DateUtil.toLocalDateTime(now);
    }

    /**
     * 获取最近一次结算周期开始时间
     */
    public LocalDateTime getLatestSettlementCycleBegin() {
        DateTime settlementCycleBegin = DateTime.now();
        int dayOfMonth = settlementCycleBegin.getField(DateField.DAY_OF_MONTH);

        if (dayOfMonth < 15) {
            settlementCycleBegin = settlementCycleBegin.setField(DateField.DAY_OF_MONTH, 1);
        } else {
            settlementCycleBegin = settlementCycleBegin.setField(DateField.DAY_OF_MONTH, 15);
        }

        settlementCycleBegin = DateUtil.beginOfDay(settlementCycleBegin);
        log.info("获取最近一次结算周期开始时间 = {}", settlementCycleBegin);
        return DateUtil.toLocalDateTime(settlementCycleBegin);
    }

    /**
     * 获取最近一次结算周期截止时间
     * 注意，本方法与【获取最近一次账单结算时间】不同的地方在于，本方法是获取结算周期的截止时间
     * 如2022年1月15号需要结算，本方法返回的截止时间为2022-01-14 23:59:59
     * @return
     */
    public LocalDateTime getLatestSettlementCycleEnd() {
        LocalDateTime latestSettlementDate = this.getLatestSettlementDate();
        LocalDateTime latestSettlementCycleEnd = latestSettlementDate.minusDays(1);
        latestSettlementCycleEnd = LocalDateTimeUtil.endOfDay(latestSettlementCycleEnd);
        latestSettlementCycleEnd = latestSettlementCycleEnd.withNano(0);
        log.info("获取最近一次结算周期截止时间 = {}", latestSettlementCycleEnd);
        return latestSettlementCycleEnd;
    }

    /**
     * 根据结算日期获取周期开始时间
     */
    public LocalDateTime getSettlementCycleBegin(LocalDateTime settlementDate) {
        DateTime settlementCycleBegin = DateUtil.parse(LocalDateTimeUtil.format(settlementDate, "yyyy-MM-dd HH:mm:ss"));
        int dayOfMonth = settlementCycleBegin.getField(DateField.DAY_OF_MONTH);

        if (dayOfMonth == 15) {
            settlementCycleBegin = settlementCycleBegin.setField(DateField.DAY_OF_MONTH, 1);
        } else {
            settlementCycleBegin = settlementCycleBegin.offset(DateField.MONTH, -1).setField(DateField.DAY_OF_MONTH, 15);
        }

        settlementCycleBegin = DateUtil.beginOfDay(settlementCycleBegin);
        log.info("根据结算日期获取周期开始时间 = {}", settlementCycleBegin);
        return DateUtil.toLocalDateTime(settlementCycleBegin);
    }

    /**
     * 根据结算日期获取周期截止时间
     */
    public LocalDateTime getSettlementCycleEnd(LocalDateTime settlementDate) {
        LocalDateTime settlementCycleEnd = settlementDate.minusDays(1);
        settlementCycleEnd = LocalDateTimeUtil.endOfDay(settlementCycleEnd);
        settlementCycleEnd = settlementCycleEnd.withNano(0);
        log.info("根据结算日期获取周期截止时间 = {}", settlementCycleEnd);
        return settlementCycleEnd;
    }

    /**
     * 计入账单
     */
    @InMethodLog("计入账单")
    public void addToBill(AddToBillDTO addToBillDTO) {
//        if (addToBillDTO != null) {
//            String tenantId = addToBillDTO.getTenantId();
//            if (StrUtil.isNotBlank(tenantId)) {
//                RedissonClient client = RedisUtils.getClient();
//                RLock rLock = client.getLock(RedisConstants.ZSMALL_BILL + tenantId);
//
//                try {
//                    rLock.lock(10L, TimeUnit.SECONDS);
//                    log.info("【计入账单】加锁成功 租户 = {}", tenantId);
//
//                    repeatRelation: {
//                        // 子订单、退款子订单防止重复记账
//                        RelationTypeEnum relationType = addToBillDTO.getRelationType();
//                        if (ObjectUtil.equals(relationType, RelationTypeEnum.OrderItem) || ObjectUtil.equals(relationType, RelationTypeEnum.OrderRefund)) {
//                            Long relationTargetId = addToBillDTO.getRelationTargetId();
//                            Boolean has = iBillRelationService.hasByTargetIdAndRelationType(relationTargetId, relationType);
//                            log.info("【计入账单】relationTargetId = {}, relationType = {} 是否存在重复账单 has = {}", relationTargetId, relationType, has);
//                            if (has) {
//                                break repeatRelation;
//                            }
//                        }
//                        this.lockAddToBill(addToBillDTO);
//                    }
//                } catch (RStatusCodeException e) {
//                    log.error("【计入账单】出现错误（StatusCodeException） {}", e.getMessage(), e);
//                    this.generateErrorRecord(addToBillDTO, MessageUtils.message(Locale.SIMPLIFIED_CHINESE, e.getStatusCode().getMessageCode()));
//                } catch (Exception e) {
//                    log.error("【计入账单】出现错误 {}", e.getMessage(), e);
//                    this.generateErrorRecord(addToBillDTO, e.getMessage());
//                } finally {
//                    rLock.unlock();
//                    log.info("【计入账单】解锁成功 租户 = {}", tenantId);
//                }
//            } else {
//                this.generateErrorRecord(addToBillDTO, ZSMallStatusCodeEnum.BILLING_FAILURE_UNKNOWN_USER.getMessage());
//            }
//        } else {
//            this.generateErrorRecord(addToBillDTO, "Null AddToBillDTO!");
//        }
    }

    /**
     * 加锁计入账单
     * @param addToBillDTO
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public void lockAddToBill(AddToBillDTO addToBillDTO) throws Exception {
//        String tenantId = addToBillDTO.getTenantId();
//
//        AbstractTypeEnum abstractType = addToBillDTO.getAbstractType();
//        if (abstractType == null) {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_UNKNOWN_ABSTRACT_TYPE);
//        }
//        RelationTypeEnum relationType = addToBillDTO.getRelationType();
//        String relationNote = addToBillDTO.getRelationNote();
//        if (relationType == null) {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_UNKNOWN_RELATION_TYPE);
//        }
//        BigDecimal totalAmount = addToBillDTO.getTotalAmount();
//
//        String billCycleNo;
//        if (StrUtil.isNotBlank(addToBillDTO.getAppointCycleNo())) {
//            billCycleNo = addToBillDTO.getAppointCycleNo();
//            log.info("【计入账单】指定账单周期编号 {}", billCycleNo);
//        } else {
//            LocalDateTime latestSettlementDate = this.getLatestSettlementDate();
//            billCycleNo = LocalDateTimeUtil.format(latestSettlementDate, "yyyyMMdd");
//            log.info("【计入账单】当前时间最近一次结算日期为：{}，账单周期编号 {}", latestSettlementDate, billCycleNo);
//        }
//
//        Bill query = new Bill();
//        query.setTenantId(tenantId);
//        query.setBillCycleNo(billCycleNo);
//
//        List<Bill> billList = iBillService.queryByEntityNotTenant(query);
//        if (CollUtil.isNotEmpty(billList)) {
//            Bill bill = billList.get(0);
//            Long billId = bill.getId();
//            BigDecimal currentIncome = bill.getCurrentIncome();
//            BigDecimal currentExpenditure = bill.getCurrentExpenditure();
//            BigDecimal currentCircularDeposit = bill.getCurrentCircularDeposit();
//            BigDecimal previousCircularDeposit = bill.getPreviousCircularDeposit();
//            BigDecimal circularDepositRatio = bill.getCircularDepositRatio();
//            log.info("【计入账单】账单原始信息 = {}", JSONUtil.toJsonStr(bill));
//
//            // 计为收入
//            if (ObjectUtil.equals(abstractType, AbstractTypeEnum.Income)) {
//                // 本期收入新值
//                BigDecimal new_CurrentIncome = NumberUtil.add(currentIncome, totalAmount);
//                // 本期实际收入 = 收入 - 支出
//                BigDecimal actualIncome = NumberUtil.sub(new_CurrentIncome, currentExpenditure);
//                // 本期实际收入乘以循环保证金比例，产生新的循环保证金，将计入本期循环保证金字段中N
//                BigDecimal new_CurrentCircularDeposit = BigDecimal.ZERO;
//                // 只有本期实际收入大于0时，才会计算循环保证金，否则本期循环保证金为0
//                if (NumberUtil.isGreater(actualIncome, BigDecimal.ZERO)) {
//                    new_CurrentCircularDeposit = NumberUtil.mul(actualIncome, circularDepositRatio);
//                }
//
//                // 本期总金额新值（本期实际收入 - 本期循环保证金 + 上期循环保证金）
//                BigDecimal new_CurrentTotalAmount = actualIncome.subtract(new_CurrentCircularDeposit).add(previousCircularDeposit);
//                log.info("【计入账单】本期收入新值 = {}, 本期循环保证金新值 = {}, 本期总金额新值 = {}", new_CurrentIncome, new_CurrentCircularDeposit,
//                    new_CurrentTotalAmount);
//
//                bill.setCurrentIncome(new_CurrentIncome);
//                bill.setCurrentCircularDeposit(new_CurrentCircularDeposit);
//                bill.setCurrentTotalAmount(new_CurrentTotalAmount);
//            } else {  // 计为支出
//                // 本期支出新值
//                BigDecimal new_CurrentExpenditure = NumberUtil.add(currentExpenditure, totalAmount);
//                // 本期实际收入 = 收入 - 支出
//                BigDecimal actualIncome = NumberUtil.sub(currentIncome, new_CurrentExpenditure);
//                // 本期实际收入乘以循环保证金比例，产生新的循环保证金，将计入本期循环保证金字段中
//                BigDecimal new_CurrentCircularDeposit = BigDecimal.ZERO;
//                // 只有本期实际收入大于0时，才会计算循环保证金，否则本期循环保证金为0
//                if (NumberUtil.isGreater(actualIncome, BigDecimal.ZERO)) {
//                    new_CurrentCircularDeposit = NumberUtil.mul(actualIncome, circularDepositRatio);
//                }
//
//                // 本期总金额新值（本期收入 - 本期支出 - 本期循环保证金 + 上期循环保证金）
//                BigDecimal new_CurrentTotalAmount = actualIncome.subtract(new_CurrentCircularDeposit).add(previousCircularDeposit);
//                log.info("【计入账单】本期支出新值 = {}, 本期循环保证金新值 = {}, 本期总金额新值 = {}", new_CurrentExpenditure, new_CurrentCircularDeposit, new_CurrentTotalAmount);
//
//                bill.setCurrentExpenditure(new_CurrentExpenditure);
//                bill.setCurrentCircularDeposit(new_CurrentCircularDeposit);
//                bill.setCurrentTotalAmount(new_CurrentTotalAmount);
//            }
//
//            BillAbstract billAbstract = iBillAbstractService.queryByBillIdAndAbstractType(billId, abstractType);
//            BigDecimal abstractTotalAmount = billAbstract.getAbstractTotalAmount();
//            log.info("【计入账单】摘要总金额 = {}, 摘要类型 = {}", abstractTotalAmount, abstractType);
//            BigDecimal new_AbstractTotalAmount = NumberUtil.add(abstractTotalAmount, totalAmount);
//            log.info("【计入账单】新的摘要总金额 = {}, 摘要类型 = {}", new_AbstractTotalAmount, abstractType);
//            billAbstract.setAbstractTotalAmount(new_AbstractTotalAmount);
//            Long billAbstractId = billAbstract.getId();
//
//            // 处理摘要详情字段和关系详情字段
//            List<AddToBillDTO.BillAbstractField> abstractFieldList = addToBillDTO.getAbstractFieldList();
//            List<BillAbstractDetail> abstractDetailList = new ArrayList<>();
//
//            // 遍历每个摘要字段
//            for (AddToBillDTO.BillAbstractField billAbstractField : abstractFieldList) {
//                AbstractFieldTypeEnum fieldType = billAbstractField.getFieldType();
//                BigDecimal fieldValue = billAbstractField.getFieldValue();
//
//                BillAbstractDetail abstractDetail =
//                    iBillAbstractDetailService.queryByBillAbstractIdAndFieldType(billAbstractId, fieldType);
//                Long abstractDetailId = abstractDetail.getId();
//                if (abstractDetailId == null) {
//                    // 摘要详情为空，说明为第一次记录该类型的摘要，为了保险，需要遍历有已存在的同类型Relation，计算出总价
//                    BigDecimal originFieldValue = iBillRelationService.queryRelationTypeTotalAmount(billId, relationType.name(),
//                        RelationFieldTypeEnum.TotalAmount);
//                    BigDecimal newFieldValue = NumberUtil.add(originFieldValue, fieldValue);
//                    abstractDetail.setFieldValue(newFieldValue);
//                    abstractDetailList.add(abstractDetail);
//                } else {
//                    BigDecimal originFieldValue = abstractDetail.getFieldValue();
//                    BigDecimal newFieldValue = NumberUtil.add(originFieldValue, fieldValue);
//                    log.info("账单摘要详情 fieldType = {}, originFieldValue = {}, newFieldValue = {}", fieldType, originFieldValue,
//                        newFieldValue);
//                    abstractDetail.setFieldValue(newFieldValue);
//                    abstractDetailList.add(abstractDetail);
//                }
//            }
//            log.info("【计入账单】准备保存的摘要详情集合 = {}", JSONUtil.toJsonStr(abstractDetailList));
//
//            BillRelation billRelation = new BillRelation();
//            billRelation.setBillId(billId);
//            billRelation.setNotes(relationNote);
//            billRelation.setRelationType(relationType);
//            billRelation.setBelongAbstractType(billAbstract.getAbstractType());
//            billRelation.setTargetId(addToBillDTO.getRelationTargetId());
//            iBillRelationService.save(billRelation);
//            Long billRelationId = billRelation.getId();
//
//            // 遍历每个关系字段
//            List<AddToBillDTO.BillRelationField> relationFieldList = addToBillDTO.getRelationFieldList();
//            List<BillRelationDetail> relationDetailList = new ArrayList<>();
//            for (AddToBillDTO.BillRelationField billRelationField : relationFieldList) {
//                RelationFieldTypeEnum fieldType = billRelationField.getFieldType();
//                String fieldValue = billRelationField.getFieldValue();
//
//                BillRelationDetail newRelationDetail = new BillRelationDetail();
//                newRelationDetail.setBillRelationId(billRelationId);
//                newRelationDetail.setFieldType(fieldType);
//                newRelationDetail.setFieldValue(fieldValue);
//                relationDetailList.add(newRelationDetail);
//            }
//            log.info("【计入账单】准备保存的关系详情集合 = {}", JSONUtil.toJsonStr(relationDetailList));
//
//            TenantHelper.ignore(() -> iBillService.saveOrUpdate(bill));
//            iBillAbstractService.saveOrUpdate(billAbstract);
//            iBillAbstractDetailService.saveOrUpdateBatch(abstractDetailList);
//
//            iBillRelationService.saveOrUpdate(billRelation);
//            iBillRelationDetailService.saveOrUpdateBatch(relationDetailList);
//
//            Bill nextQuery = new Bill();
//            nextQuery.setPreviousBillId(billId);
//
//            // 查询是否存在已经生成的下期账单，若已经生成，则将本期循环保证金计入下期账单的“上期循环保证金”中
//            List<Bill> nextBillList = iBillService.queryByEntityNotTenant(nextQuery);
//            if (CollUtil.isNotEmpty(nextBillList)) {
//                Bill nextBill = nextBillList.get(0);
//                nextBill.setPreviousCircularDeposit(bill.getCurrentCircularDeposit());
//                BigDecimal nextIncome = nextBill.getCurrentIncome();
//                BigDecimal nextExpenditure = nextBill.getCurrentExpenditure();
//                BigDecimal nextCurrentCircularDeposit = nextBill.getCurrentCircularDeposit();
//                BigDecimal nextPreviousCircularDeposit = nextBill.getPreviousCircularDeposit();
//
//                // 下期实际收入 = 收入 - 支出
//                BigDecimal new_CurrentTotalAmount = NumberUtil.sub(nextIncome, nextExpenditure, nextCurrentCircularDeposit).add(nextPreviousCircularDeposit);
//                nextBill.setCurrentTotalAmount(new_CurrentTotalAmount);
//
//                TenantHelper.ignore(() -> iBillService.saveOrUpdate(nextBill));
//
//                BillLog nextBillLog = BeanUtil.toBean(nextBill, BillLog.class);
//                nextBillLog.setId(null);
//                nextBillLog.setOriginBillId(nextBill.getId());
//                TenantHelper.ignore(() -> iBillLogService.saveOrUpdate(nextBillLog));
//            }
//
//            this.generateLog(bill, billAbstract, abstractDetailList, billRelation, relationDetailList);
//        } else {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_NO_BILL_FOUND);
//        }
    }

    /**
     * 记账-子订单
     * @param orderItems
     * @return
     */
    public void generateBillDTOByOrderItem(List<OrderItem> orderItems, String appointCycleNo) {
//        for (OrderItem saveOrderItem : orderItems) {
//            log.info("记账-子订单 saveOrderItem = {}", saveOrderItem.toString());
//            Long orderItemId = saveOrderItem.getId();
//            try {
//                OrderStateType orderState = saveOrderItem.getOrderState();
//                // 供货商应得收入
//                BigDecimal totalPriceSup = saveOrderItem.getSupplierIncomeEarned();
//                // 直接取下单时数量
//                Integer actualQuantity = saveOrderItem.getTotalQuantity();
//
//                AddToBillDTO billDTO = this.generateDTOByOrderItem(saveOrderItem, totalPriceSup, actualQuantity, AbstractTypeEnum.Income, RelationTypeEnum.OrderItem);
//                billDTO.setAppointCycleNo(appointCycleNo);
//                this.addToBill(billDTO);
//
//                // 子订单入账，需要把关联的退款单全部带着入账
//                if (ObjectUtil.equals(orderState, OrderStateType.Paid)) {
//                    List<OrderRefund> orderRefundList = iOrderRefundService.queryByOrderItemAndState(saveOrderItem.getId(), OrderRefundStateType.Refunded);
//                    if (CollUtil.isNotEmpty(orderRefundList)) {
//                        for (OrderRefund orderRefund : orderRefundList) {
//                            String orderRefundNo = orderRefund.getOrderRefundNo();
//                            // 原始退款金额（供货商）
//                            BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
//
//                            AddToBillDTO billDTO_refund = this.generateDTOByOrderRefund(saveOrderItem, orderRefund, originalRefundAmount,
//                                AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                            this.addToBill(billDTO_refund);
//                        }
//                    }
//                }
//            } catch (RStatusCodeException e) {
//                log.error("子账单记账失败（RStatusCodeException） orderItemId = {} error {}", orderItemId, e.getMessage(), e);
//                BillErrorRecord billErrorRecord = new BillErrorRecord();
//                billErrorRecord.setRelationType(RelationTypeEnum.OrderItem.name());
//                billErrorRecord.setRelationTargetId(orderItemId);
//                billErrorRecord.setErrorMessage(e.getStatusCode().getMessage());
//                iBillErrorRecordService.save(billErrorRecord);
//            } catch (Exception e) {
//                log.error("子账单记账失败 orderItemId = {} error {}", orderItemId, e.getMessage(), e);
//                BillErrorRecord billErrorRecord = new BillErrorRecord();
//                billErrorRecord.setRelationType(RelationTypeEnum.OrderItem.name());
//                billErrorRecord.setRelationTargetId(orderItemId);
//                billErrorRecord.setErrorMessage(e.getMessage());
//                iBillErrorRecordService.save(billErrorRecord);
//            }
//        }
    }

    /**
     * 记账-现货订单
     * @param order
     * @param orderItems
     * @param appointCycleNo 指定周期编号
     * @return
     */
    public void generateBillDTOByWholesaleOrder(Orders order, List<OrderItem> orderItems, WholesaleIntentionOrder wiOrder, List<WholesaleIntentionOrderItem> wiOrderItems, String appointCycleNo) {
//        String orderNo = order.getOrderNo();
//        log.info("记账-现货订单，OrderNo = {}", orderNo);
//        Long orderId = order.getId();
//
//         try {
//             Product product = iProductService.queryByProductCodeNotTenant(wiOrder.getProductCode());
//             Date createDateTime = order.getCreateTime();
//             AddToBillDTO billDTO = generateDTOByWholesaleOrder(product.getTenantId(), orderNo, wiOrder, orderItems,
//                 wiOrderItems, createDateTime, appointCycleNo);
//             this.addToBill(billDTO);
//
//             OrderStateType orderStatus = order.getOrderState();
//             // 现货订单入账，需要把关联的退款单全部带着入账
//             if (ObjectUtil.equals(orderStatus, OrderStateType.Paid)) {
//                 List<OrderRefund> orderRefunds = iOrderRefundService.queryByOrderAndState(orderId, OrderRefundStateType.Refunded);
//
//                 if (CollUtil.isNotEmpty(orderRefunds)) {
//                     for (OrderRefund orderRefund : orderRefunds) {
//                         String orderRefundNo = orderRefund.getOrderRefundNo();
//                         BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
//
//                         List<OrderRefundItem> orderRefundItemList = iOrderRefundItemService.queryByOrderRefundNo(orderRefund.getOrderRefundNo());
//                         AddToBillDTO billDTO_others_refund = this.generateDTOByOrderRefund(orderRefund, orderRefundItemList, orderRefundNo, originalRefundAmount,
//                             AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                         this.addToBill(billDTO_others_refund);
//                     }
//                 }
//             }
//         } catch (Exception e) {
//             log.error("现货订单账单记账失败 orderNo = {} error {}", orderNo, e.getMessage(), e);
//             BillErrorRecord billErrorRecordEntity = new BillErrorRecord();
//             billErrorRecordEntity.setRelationType(RelationTypeEnum.OrderItem.name());
//             billErrorRecordEntity.setRelationTargetId(orderId);
//             billErrorRecordEntity.setErrorMessage(e.getMessage());
//             iBillErrorRecordService.save(billErrorRecordEntity);
//         }
    }

    /**
     * 记账-现货订单退款单
     * @param orderRefund
     */
     public void generateBillDTOByWholesaleOrderRefund(OrderRefund orderRefund, List<OrderRefundItem> orderRefundItemList) {
//         Long orderId = orderRefund.getOrderId();
//         String orderNo = orderRefund.getOrderNo();
//         try {
//             Orders orders = iOrdersService.getByOrderNo(orderNo);
//             Date createTime = orders.getCreateTime();
//             String orderRefundNo = orderRefund.getOrderRefundNo();
//             BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
//
//             LogisticsProgress fulfillment = orders.getFulfillmentProgress();
//             OrderStateType orderState = orders.getOrderState();
//
//             WholesaleIntentionOrder wiOrder =
//                 iWholesaleIntentionOrderService.queryByOrderNo(orders.getOrderNo());
//
//             // 订单已完成，退款单单独入账
//             if (ObjectUtil.equals(fulfillment, LogisticsProgress.Fulfilled)) {
//                 AddToBillDTO billDTO = this.generateDTOByOrderRefund(orderRefund, orderRefundItemList, orderRefundNo,
//                     originalRefundAmount, AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                 this.addToBill(billDTO);
//             } else {
//                 // 订单未完成，需要判断主订单状态，若是已全额退款，则需要入账正向订单，并且连带所有其他的逆向单一起入账
//                 if (ObjectUtil.equals(orderState, OrderStateType.Refunded)) {
//                     List<WholesaleIntentionOrderItem> wiOrderItems =
//                         iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrder.getId());
//
//                     List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orderId);
//
//                     // 正向订单入账
//                     AddToBillDTO billDTO_order = generateDTOByWholesaleOrder(LoginHelper.getTenantId(), orderNo, wiOrder, orderItems,
//                         wiOrderItems, createTime, null);
//                     this.addToBill(billDTO_order);
//
//                     // 所有关联的其它已退款的逆向订单入账
//                     List<OrderRefund> allOrderRefunds = iOrderRefundService.queryOrderRefundByRefunded(orderId, orderRefund.getId(), OrderRefundStateType.Refunded.name());
//
//                     // 将当前逆向订单加入数组
//                     allOrderRefunds.add(orderRefund);
//                     for (OrderRefund one_orderRefund : allOrderRefunds) {
//                         String one_orderRefundNo = one_orderRefund.getOrderRefundNo();
//                         BigDecimal one_refundPrice = one_orderRefund.getOriginalRefundAmount();
//                         List<OrderRefundItem> one_orderRefundItemList = iOrderRefundItemService.queryByOrderRefundNo(one_orderRefundNo);
//
//                         AddToBillDTO billDTO_others_refund = this.generateDTOByOrderRefund(one_orderRefund, one_orderRefundItemList, orderRefundNo, one_refundPrice,
//                             AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                         this.addToBill(billDTO_others_refund);
//                     }
//                 }
//             }
//         } catch (RStatusCodeException e) {
//             log.error("退款主单记账失败（StatusCodeException） orderId = {} error {}", orderId, e.getMessage(), e);
//             BillErrorRecord billErrorRecord = new BillErrorRecord();
//             billErrorRecord.setRelationType(RelationTypeEnum.OrderRefund.name());
//             billErrorRecord.setRelationTargetId(orderId);
//             billErrorRecord.setErrorMessage(e.getStatusCode().getMessage());
//             iBillErrorRecordService.save(billErrorRecord);
//         } catch (Exception e) {
//             log.error("退款主单记账失败 orderId = {} error {}", orderId, e.getMessage(), e);
//             BillErrorRecord billErrorRecord = new BillErrorRecord();
//             billErrorRecord.setRelationType(RelationTypeEnum.OrderRefund.name());
//             billErrorRecord.setRelationTargetId(orderId);
//             billErrorRecord.setErrorMessage(e.getMessage());
//             iBillErrorRecordService.save(billErrorRecord);
//         }
     }

    /**
     * 记账-退款子单（普通订单用）
     * @param orderRefundItemList
     */
    public void generateBillDTOByOrderRefundItem(List<OrderRefundItem> orderRefundItemList) {
//        for (OrderRefundItem orderRefundItem : orderRefundItemList) {
//
//            Long orderRefundItemId = orderRefundItem.getId();
//            Long orderRefundId = orderRefundItem.getOrderRefundId();
//            Long orderItemId = orderRefundItem.getOrderItemId();
//
//            OrderRefund orderRefund = iOrderRefundService.queryByIdNotTenant(orderRefundId);
//
//            // 原始退款金额（供货商）
//            BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
//            OrderItem orderItem = iOrderItemService.queryByIdNotTenant(orderItemId);
//
//            try {
//                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
//                OrderStateType orderState = orderItem.getOrderState();
//
//                // 订单已完成，退款单单独入账
//                if (ObjectUtil.equals(fulfillment, LogisticsProgress.Fulfilled)) {
//                    AddToBillDTO billDTO = this.generateDTOByOrderRefund(orderItem, orderRefund, originalRefundAmount,
//                        AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                    this.addToBill(billDTO);
//                } else {
//                    // 订单未完成，需要判断子订单状态，若是已全额退款，则需要入账正向订单，并且连带所有其他的逆向单一起入账
//                    if (ObjectUtil.equals(orderState, OrderStateType.Refunded)) {
//                        String activityCode = orderItem.getActivityCode();
//                        BigDecimal supplierIncomeEarned = orderItem.getSupplierIncomeEarned();
//                        Integer totalQuantity = orderItem.getTotalQuantity();
//
//                        // 正向订单入账
//                        // 未发货就全额退款的活动订单，不需要退还订金
//                        if (StrUtil.isNotBlank(activityCode) && ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)) {
//                            supplierIncomeEarned = NumberUtil.sub(supplierIncomeEarned, orderItem.getOriginalPrepaidTotalAmount());
//                        }
//                        AddToBillDTO billDTO_orderItem = this.generateDTOByOrderItem(orderItem, supplierIncomeEarned,
//                            totalQuantity, AbstractTypeEnum.Income, RelationTypeEnum.OrderItem);
//                        this.addToBill(billDTO_orderItem);
//
//                        // 所有关联的逆向订单入账
//                        List<OrderRefund> allOrderRefund = iOrderRefundService.queryByOrderItemAndState(orderItemId, OrderRefundStateType.Refunded);
//
//                        // 将当前逆向订单加入数组
//                        for (OrderRefund one_orderRefund : allOrderRefund) {
//                            String one_orderRefundNo = one_orderRefund.getOrderRefundNo();
//                            BigDecimal one_originalRefundAmount = one_orderRefund.getOriginalRefundAmount();
//                            AddToBillDTO billDTO_others_refund = this.generateDTOByOrderRefund(orderItem, one_orderRefund, one_originalRefundAmount,
//                                AbstractTypeEnum.Expenditure, RelationTypeEnum.OrderRefund);
//                            this.addToBill(billDTO_others_refund);
//                        }
//                    }
//                }
//            } catch (RStatusCodeException e) {
//                log.error("退款子单记账失败（RStatusCodeException） orderRefundItemId = {} error {}", orderRefundItemId, e.getMessage(), e);
//                BillErrorRecord billErrorRecord = new BillErrorRecord();
//                billErrorRecord.setRelationType(RelationTypeEnum.OrderRefund.name());
//                billErrorRecord.setRelationTargetId(orderRefundItemId);
//                billErrorRecord.setErrorMessage(e.getStatusCode().getMessage());
//                iBillErrorRecordService.save(billErrorRecord);
//            } catch (Exception e) {
//                log.error("退款子单记账失败 orderRefundItemId = {} error {}", orderRefundItemId, e.getMessage(), e);
//                BillErrorRecord billErrorRecord = new BillErrorRecord();
//                billErrorRecord.setRelationType(RelationTypeEnum.OrderRefund.name());
//                billErrorRecord.setRelationTargetId(orderRefundItemId);
//                billErrorRecord.setErrorMessage(e.getMessage());
//                iBillErrorRecordService.save(billErrorRecord);
//            }
//        }
    }

    /**
     * 记账-员工汇入或者扣除钱包余额
     * @param transactionRecord
     */
    public void generateBillDTOByRechargeOrDeduct(String userCodeMd, TransactionRecord transactionRecord) {
//        Long transactionsId = transactionRecord.getId();
//        Date createDateTime = transactionRecord.getCreateTime();
//
//        String tenantId = transactionRecord.getTenantId();
//        TransactionTypeEnum transactionType = transactionRecord.getTransactionType();
//        TransactionSubTypeEnum transactionSubType = transactionRecord.getTransactionSubType();
//        String transactionNote = transactionRecord.getTransactionNote();
//        BigDecimal transactionAmount = transactionRecord.getTransactionAmount();
//
//        AddToBillDTO billDTO = null;
//        if (ObjectUtil.equals(transactionSubType, TransactionSubTypeEnum.PlatformDeduct)) {
//            billDTO = AddToBillDTO.init(tenantId, transactionAmount, AbstractTypeEnum.Expenditure, RelationTypeEnum.PlatformDeduct, transactionsId);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_PlatformDeduction, transactionAmount);
//        } else if (ObjectUtil.equals(transactionSubType, TransactionSubTypeEnum.PlatformRemit)) {
//            billDTO = AddToBillDTO.init(tenantId, transactionAmount, AbstractTypeEnum.Income, RelationTypeEnum.PlatformRemit, transactionsId);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_PlatformRecharge, transactionAmount);
//        }
//
//        if (billDTO != null) {
//            billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createDateTime, dateTimeFormatter));
//            billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, transactionAmount);
//            billDTO.addRelationField(RelationFieldTypeEnum.TransactionNote, transactionNote);
//            String note = "操作员工编号：" + userCodeMd;
//            billDTO.setRelationNote(note);
//            this.addToBill(billDTO);
//        }

    }

    /**
     * 记账-活动结账记录
     * @param checkout
     */
    public void generateBillDTOByProductActivityCheckout(ProductActivityCheckout checkout, String appointCycleNo) {
//        log.info("记账-活动结算记录 checkout = {}", JSONUtil.toJsonStr(checkout));
//        Long activityId = checkout.getProductActivityId();
//        Long activityItemId = checkout.getProductActivityItemId();
//        Integer checkoutQuantity = checkout.getCheckoutQuantity();
//
//        ProductActivity productActivity =
//            iProductActivityService.queryOneByEntity(ProductActivity.builder().id(activityId).build());
//        String productSkuCode = productActivity.getProductSkuCode();
//
//        String activityCode = productActivity.getActivityCode();
//        String activityName = productActivity.getActivityName();
//        ProductActivityTypeEnum activityType = productActivity.getActivityType();
//
//        Date createTime = checkout.getCreateTime();
//        BigDecimal checkoutAmount = checkout.getCheckoutAmount();
//
//        ActivityCheckoutTypeEnum checkoutType = checkout.getCheckoutType();
//        String sTenantId = checkout.getSupplierTenantId();
//
//        AddToBillDTO billDTO = null;
//        if (ActivityCheckoutTypeEnum.PenaltyFee.equals(checkoutType)) {
//            billDTO = AddToBillDTO.init(sTenantId, checkoutAmount, AbstractTypeEnum.Income, RelationTypeEnum.ActivityPenaltyFee, activityItemId);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_ActivityPenaltyFee, checkoutAmount);
//        } else if (ActivityCheckoutTypeEnum.StorageFee.equals(checkoutType)) {
//            billDTO = AddToBillDTO.init(sTenantId, checkoutAmount, AbstractTypeEnum.Income, RelationTypeEnum.ActivityStorageFee, activityItemId);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_ActivityStorageFee, checkoutAmount);
//        }
//        billDTO.setAppointCycleNo(appointCycleNo);
//
//        billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createTime, dateTimeFormatter));
//        billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, checkoutAmount);
//        billDTO.addRelationField(RelationFieldTypeEnum.ItemNo, productSkuCode);
//        billDTO.addRelationField(RelationFieldTypeEnum.ProductQuantity, checkoutQuantity != null ? checkoutQuantity.toString() : null);
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityCode, activityCode);
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityName, activityName);
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityType, activityType.name());
//        this.addToBill(billDTO);
    }

    // TODO 二期实现
    // /**
    //  * 记账-清货订单
    //  * @param liquidationSettled
    //  */
    // public void generateBillDTOByLiquidationSettled(LiquidationSettledEntity liquidationSettled) {
    //     log.info("记账-清货订单 liquidationSettled = {}", JSONUtil.toJsonStr(liquidationSettled));
    //     Long storeIdSup = liquidationSettled.getStoreIdSup();
    //     BigDecimal originAmount = liquidationSettled.getOriginAmount();
    //     Long liquidationId = liquidationSettled.getLiquidationId();
    //     Long liquidationOrderId = liquidationSettled.getLiquidationOrderId();
    //
    //     LiquidationEntity liquidation = iLiquidationService.getById(liquidationId);
    //     LiquidationOrderEntity liquidationOrder = iLiquidationOrderService.getById(liquidationOrderId);
    //
    //     // 清货单货款账单记录
    //     AddToBillDTO billDTO = AddToBillDTO.init(storeIdSup, originAmount, AbstractTypeEnum.Income, RelationTypeEnum.LiquidationOrder, liquidationOrderId);
    //
    //     String liquidationCode = liquidation.getLiquidationCode();
    //     LocalDateTime createDateTime = liquidationOrder.getCreateDateTime();
    //     String liquidationOrderNo = liquidationOrder.getLiquidationOrderNo();
    //
    //     BigDecimal orderTotalPrice = liquidationOrder.getOrderTotalPrice();
    //     BigDecimal orderPalletFee = liquidationOrder.getOrderPalletFee();
    //     BigDecimal orderShippingFee = liquidationOrder.getOrderShippingFee();
    //     BigDecimal orderOperationFee = liquidationOrder.getOrderOperationFee();
    //     BigDecimal actualTotalPrice = liquidationOrder.getActualTotalPrice();
    //     Integer totalQuantity = liquidationOrder.getTotalQuantity();
    //
    //     billDTO.addAbstractField(AbstractFieldTypeEnum.Total_ProductAmount, orderTotalPrice);
    //     billDTO.addAbstractField(AbstractFieldTypeEnum.Total_OperationFee, orderOperationFee);
    //     billDTO.addAbstractField(AbstractFieldTypeEnum.Total_FinalDeliveryFee, orderShippingFee);
    //     billDTO.addAbstractField(AbstractFieldTypeEnum.Total_PalletFee, orderPalletFee);
    //
    //     billDTO.addRelationField(RelationFieldTypeEnum.LiquidationCode, liquidationCode);
    //     billDTO.addRelationField(RelationFieldTypeEnum.LiquidationOrderNo, liquidationOrderNo);
    //     billDTO.addRelationField(RelationFieldTypeEnum.ProductQuantity, NumberUtil.toStr(totalQuantity));
    //     billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, LocalDateTimeUtil.format(createDateTime, dateTimeFormatter));
    //
    //     billDTO.addRelationField(RelationFieldTypeEnum.ProductAmount, orderTotalPrice);
    //     billDTO.addRelationField(RelationFieldTypeEnum.OperationFee, orderOperationFee);
    //     billDTO.addRelationField(RelationFieldTypeEnum.FinalDeliveryFee, orderShippingFee);
    //     billDTO.addRelationField(RelationFieldTypeEnum.LiquidationPalletFee, orderPalletFee);
    //     billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, actualTotalPrice);
    //
    //     this.addToBill(billDTO);
    //
    //     // 开始生成支出（清货佣金）账单记录
    //     String valueFromDouble = businessParameterService.getValueFromString(BusinessParameterType.WITHDRAWAL_HANDLING_FEE);
    //     // 佣金比例
    //     BigDecimal commissionRatio = NumberUtil.toBigDecimal(valueFromDouble);
    //     BigDecimal commission = NumberUtil.mul(actualTotalPrice, commissionRatio).setScale(2, RoundingMode.HALF_UP);
    //     // 乘以100准备做展示用
    //     BigDecimal commissionRatio_100 = NumberUtil.mul(commissionRatio, 100);
    //
    //     AddToBillDTO commissionBillDTO = AddToBillDTO.init(storeIdSup, commission, AbstractTypeEnum.Expenditure, RelationTypeEnum.LiquidationCommission, liquidationOrderId);
    //     commissionBillDTO.addAbstractField(AbstractFieldTypeEnum.Total_CommissionFee, commission);
    //
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.LiquidationCode, liquidationCode);
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.LiquidationOrderNo, liquidationOrderNo);
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, LocalDateTimeUtil.format(createDateTime, dateTimeFormatter));
    //
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.LiquidationOrderActualTotalPrice, actualTotalPrice);
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.LiquidationCommissionRatio, NumberUtil.toStr(commissionRatio_100) + "%");
    //     commissionBillDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, commission);
    //     this.addToBill(commissionBillDTO);
    // }


    /**
     * 生成记账错误记录
     * @param addToBillDTO
     * @param errorMessage
     */
    private void generateErrorRecord(AddToBillDTO addToBillDTO, String errorMessage) {
//        BillErrorRecord billErrorRecord = new BillErrorRecord();
//        if (addToBillDTO != null) {
//            billErrorRecord.setTenantId(addToBillDTO.getTenantId());
//            billErrorRecord.setRelationType(addToBillDTO.getRelationType() != null ? addToBillDTO.getRelationType().name() : null);
//            billErrorRecord.setRelationTargetId(addToBillDTO.getRelationTargetId());
//            billErrorRecord.setBillBody(JSONUtil.parseObj(addToBillDTO));
//            billErrorRecord.setErrorMessage(errorMessage);
//        } else {
//            billErrorRecord.setErrorMessage("空的AddToBillDTO!");
//        }
//        iBillErrorRecordService.save(billErrorRecord);
    }

    private void generateLog(Bill bill, BillAbstract billAbstract,
                             List<BillAbstractDetail> abstractDetailList, BillRelation billRelation,
                             List<BillRelationDetail> relationDetailList) {
//        BillLog billLog = BeanUtil.toBean(bill, BillLog.class);
//        billLog.setId(null);
//        billLog.setOriginBillId(bill.getId());
//
//        TenantHelper.ignore(() -> iBillLogService.saveOrUpdate(billLog));
//        Long billLogId = billLog.getId();
//
//        BillAbstractLog billAbstractLog = BeanUtil.toBean(billAbstract, BillAbstractLog.class);
//        billAbstractLog.setId(null);
//        billAbstractLog.setBillLogId(billLogId);
//        iBillAbstractLogService.save(billAbstractLog);
//        Long billAbstractLogId = billAbstractLog.getId();
//
//        List<BillAbstractDetailLog> abstractDetailLogList = new ArrayList<>();
//        for (BillAbstractDetail billAbstractDetail : abstractDetailList) {
//            BillAbstractDetailLog billAbstractDetailLog =
//                BeanUtil.toBean(billAbstractDetail, BillAbstractDetailLog.class);
//            billAbstractDetailLog.setId(null);
//            billAbstractDetailLog.setBillAbstractLogId(billAbstractLogId);
//            abstractDetailLogList.add(billAbstractDetailLog);
//        }
//        iBillAbstractDetailLogService.saveBatch(abstractDetailLogList);
//
//        BillRelationLog billRelationLog = BeanUtil.toBean(billRelation, BillRelationLog.class);
//        billRelationLog.setId(null);
//        billRelationLog.setBillLogId(billLogId);
//        iBillRelationLogService.save(billRelationLog);
//        Long billRelationLogId = billRelationLog.getId();
//
//        List<BillRelationDetailLog> relationDetailLogList = new ArrayList<>();
//        for (BillRelationDetail billRelationDetail : relationDetailList) {
//            BillRelationDetailLog billRelationDetailLog =
//                BeanUtil.toBean(billRelationDetail, BillRelationDetailLog.class);
//            billRelationDetailLog.setId(null);
//            billRelationDetailLog.setBillRelationLogId(billRelationLogId);
//            relationDetailLogList.add(billRelationDetailLog);
//        }
//        iBillRelationDetailLogService.saveBatch(relationDetailLogList);
    }

    /**
     * 根据子订单生成记账DTO
     * @param saveOrderItem
     * @param Total_Amount
     * @param abstractTypeEnum
     * @param relationTypeEnum
     * @return
     */
//    private AddToBillDTO generateDTOByOrderItem(OrderItem saveOrderItem, BigDecimal Total_Amount, Integer actualQuantity, AbstractTypeEnum abstractTypeEnum,
//                                                RelationTypeEnum relationTypeEnum) throws Exception {
//        Date createDateTime = saveOrderItem.getCreateTime();
//        Long orderItemId = saveOrderItem.getId();
//
//        // 判断是否是自提
//        LogisticsTypeEnum logisticsType = saveOrderItem.getLogisticsType();
//
//        Orders orders = iOrdersService.queryById(saveOrderItem.getOrderId());
//        String orderNo = orders.getOrderNo();
//        String tenantId = saveOrderItem.getSupplierTenantId();
//
//        String activityCode = saveOrderItem.getActivityCode();
//        String itemNo = saveOrderItem.getProductSkuCode();
//
//        BigDecimal Total_ProductAmount = BigDecimal.ZERO;
//        BigDecimal Total_OperationFee = BigDecimal.ZERO;
//        BigDecimal Total_FinalDeliveryFee = BigDecimal.ZERO;
//
//        AddToBillDTO billDTO = AddToBillDTO.init(tenantId, Total_Amount, abstractTypeEnum, relationTypeEnum, orderItemId);
//        log.info("记账 orderItemId = {}, actualQuantity = {}, totalPriceSup = {}", orderItemId, actualQuantity, Total_Amount);
//
//        log.info("记账 orderItemId = {}, activityCode = {}", orderItemId, activityCode);
//
//        String activityCodeSup = null;
//        String activityType = null;
//        if (StrUtil.isNotBlank(activityCode)) {
//            ProductActivityPriceItem activityItemPrice =
//                iProductActivityPriceItemService.getByActivityCode(activityCode);
//
//            if (activityItemPrice != null) {
//                // 查询并记录供货商的活动编号和活动类型
//                ProductActivityItem productActivityItem =
//                    iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).build());
//                activityCodeSup = productActivityItem.getActivityCodeParent();
//                activityType = productActivityItem.getActivityType().name();
//
//                BigDecimal activityUnitPrice = activityItemPrice.getActivityUnitPrice();
//                BigDecimal activityOperationFee = activityItemPrice.getActivityOperationFee();
//                // 未发货就全额退款的活动订单，不需要退还订金
//                if (ObjectUtil.equals(saveOrderItem.getOrderState(), OrderStateType.Refunded)
//                    && ObjectUtil.equals(saveOrderItem.getFulfillmentProgress(), LogisticsProgress.UnDispatched)) {
//                    // 活动订金比例
//                    JSONObject ACTIVITY_DEPOSIT_PRICE_PERCENT = businessParameterService.getValueFromJSONObject(BusinessParameterType.ACTIVITY_DEPOSIT_PRICE_PERCENT);
//                    BigDecimal depositPricePercent = new BigDecimal(ACTIVITY_DEPOSIT_PRICE_PERCENT.getStr(activityType));
//
//                    // 订金单价
//                    BigDecimal activityDepositUnitPrice = activityItemPrice.getActivityDepositUnitPrice();
//                    // 单价部分的订金
//                    BigDecimal activityUnitPrice_deposit = NumberUtil.mul(activityUnitPrice, depositPricePercent).setScale(2, RoundingMode.HALF_UP);
//                    // 操作费部分的订金
//                    BigDecimal activityOperationFee_deposit = NumberUtil.sub(activityDepositUnitPrice, activityUnitPrice_deposit);
//
//                    // 新的商品单价（因为不退订金而减去了订金的）
//                    activityUnitPrice = NumberUtil.sub(activityUnitPrice, activityUnitPrice_deposit);
//                    // 新的商品操作费单价（因为不退订金而减去了订金的）
//                    activityOperationFee = NumberUtil.sub(activityOperationFee, activityOperationFee_deposit);
//                }
//
//                BigDecimal activityFinalDeliveryFee = activityItemPrice.getActivityFinalDeliveryFee();
//
//                Total_ProductAmount = NumberUtil.mul(activityUnitPrice, actualQuantity);
//                Total_OperationFee = NumberUtil.mul(activityOperationFee, actualQuantity);
//                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                    Total_FinalDeliveryFee = BigDecimal.ZERO;
//                } else {
//                    Total_FinalDeliveryFee = NumberUtil.mul(activityFinalDeliveryFee, actualQuantity);
//                }
//            }
//        } else {
//            OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemId(orderItemId);
//            if (orderItemPrice != null) {
//                BigDecimal originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
//                BigDecimal originalOperationFee = orderItemPrice.getOriginalOperationFee();
//
//                Total_ProductAmount = NumberUtil.mul(originalUnitPrice, actualQuantity);
//                Total_OperationFee = NumberUtil.mul(originalOperationFee, actualQuantity);
//
//                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                    BigDecimal originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();
//                    Total_FinalDeliveryFee = NumberUtil.mul(originalFinalDeliveryFee, actualQuantity);
//                }
//            } else {
//                Total_ProductAmount = Total_Amount;
//                Total_OperationFee = BigDecimal.ZERO;
//                Total_FinalDeliveryFee = BigDecimal.ZERO;
//            }
//        }
//
//        log.info("记账 Total_ProductAmount = {}, Total_OperationFee = {}, Total_FinalDeliveryFee = {}", Total_ProductAmount,
//            Total_OperationFee, Total_FinalDeliveryFee);
//        if (Total_ProductAmount != null && Total_OperationFee != null && Total_FinalDeliveryFee != null) {
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_ProductAmount, Total_ProductAmount);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_OperationFee, Total_OperationFee);
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_FinalDeliveryFee, Total_FinalDeliveryFee);
//
//            billDTO.addRelationField(RelationFieldTypeEnum.OrderNo, orderNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ItemNo, itemNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCode, activityCode);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCodeSup, activityCodeSup);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityType, activityType);
//            billDTO.addRelationField(RelationFieldTypeEnum.ProductQuantity, NumberUtil.toStr(actualQuantity));
//            billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createDateTime, dateTimeFormatter));
//
//            billDTO.addRelationField(RelationFieldTypeEnum.ProductAmount, Total_ProductAmount);
//            billDTO.addRelationField(RelationFieldTypeEnum.OperationFee, Total_OperationFee);
//            billDTO.addRelationField(RelationFieldTypeEnum.FinalDeliveryFee, Total_FinalDeliveryFee);
//            billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, Total_Amount);
//            return billDTO;
//        } else {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_ORDER_PRICE_FOUND);
//        }
//    }
//
//    private AddToBillDTO generateDTOByWholesaleOrder(String supplierTenantId, String orderNo, WholesaleIntentionOrder wiOrder, List<OrderItem> orderItems,
//                                                     List<WholesaleIntentionOrderItem> wiOrderItems, Date createDateTime, String appointCycleNo) {
//
//        List<String> itemNoList =
//            wiOrderItems.stream().map(WholesaleIntentionOrderItem::getProductSkuCode).collect(Collectors.toList());
//        BigDecimal orderTotalAmount = wiOrder.getOrderTotalAmount();
//        BigDecimal productAmount = wiOrder.getProductAmount();
//        BigDecimal finalOperationFee = wiOrder.getFinalOperationFee();
//        BigDecimal finalShippingFee = wiOrder.getFinalShippingFee();
//        Integer totalQuantity = wiOrder.getTotalQuantity();
//
//        Long orderItemId = orderItems.get(0).getId();
//        AddToBillDTO billDTO = AddToBillDTO.init(supplierTenantId, orderTotalAmount, AbstractTypeEnum.Income, RelationTypeEnum.OrderItem, orderItemId);
//        billDTO.setRelationNote("订单号" + orderNo + "，此为国外现货订单，国外现货订单为主订单维度入账，为了适配账单的OrderItem关系类型，targetId字段存的是其中一个子订单的id");
//        String nullStr = null;
//
//        billDTO.addAbstractField(AbstractFieldTypeEnum.Total_ProductAmount, productAmount);
//        billDTO.addAbstractField(AbstractFieldTypeEnum.Total_OperationFee, finalOperationFee);
//        billDTO.addAbstractField(AbstractFieldTypeEnum.Total_FinalDeliveryFee, finalShippingFee);
//
//        billDTO.addRelationField(RelationFieldTypeEnum.OrderNo, orderNo);
//        billDTO.addRelationField(RelationFieldTypeEnum.ItemNo, CollUtil.join(itemNoList, ","));
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityCode);
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityCodeSup);
//        billDTO.addRelationField(RelationFieldTypeEnum.ActivityType);
//        billDTO.addRelationField(RelationFieldTypeEnum.ProductQuantity, NumberUtil.toStr(totalQuantity));
//        billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createDateTime, dateTimeFormatter));
//
//        billDTO.addRelationField(RelationFieldTypeEnum.ProductAmount, productAmount);
//        billDTO.addRelationField(RelationFieldTypeEnum.OperationFee, finalOperationFee);
//        billDTO.addRelationField(RelationFieldTypeEnum.FinalDeliveryFee, finalShippingFee);
//        billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, orderTotalAmount);
//        billDTO.setAppointCycleNo(appointCycleNo);
//        return billDTO;
//    }
//
//    /**
//     * 根据退款单生成记账DTO
//     * @param orderRefund
//     * @param orderRefundItemList
//     * @param orderRefundNo
//     * @param Total_Amount
//     * @param abstractTypeEnum
//     * @param relationTypeEnum
//     * @return
//     * @throws Exception
//     */
//    private AddToBillDTO generateDTOByOrderRefund(OrderRefund orderRefund, List<OrderRefundItem> orderRefundItemList, String orderRefundNo,
//                                                  BigDecimal Total_Amount, AbstractTypeEnum abstractTypeEnum, RelationTypeEnum relationTypeEnum) throws Exception {
//        Date createDateTime = orderRefund.getCreateTime();
//        String orderNo = orderRefund.getOrderNo();
//        String supplierTenantId = orderRefund.getSupplierTenantId();
//        List<String> itemNoList = new ArrayList<>();
//        for (OrderRefundItem orderRefundItem : orderRefundItemList) {
//            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderRefundItem.getOrderItemId());
//            itemNoList.add(orderProductSku.getProductSkuCode());
//        }
//
//        String itemNo = CollUtil.join(itemNoList, ",");
//        Long orderRefundItemId = orderRefundItemList.get(0).getId();
//        AddToBillDTO billDTO = AddToBillDTO.init(supplierTenantId, Total_Amount, abstractTypeEnum, relationTypeEnum, orderRefundItemId);
//        billDTO.setRelationNote("订单号" + orderNo + "，此为国外现货订单，国外现货订单退款入账为退款主单维度入账，为了适配账单的OrderRefundItem关系类型，targetId字段存的是其中一个退款子订单的id");
//
//        if (Total_Amount != null) {
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_RefundAmount, Total_Amount);
//
//            billDTO.addRelationField(RelationFieldTypeEnum.OrderNo, orderNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.OrderRefundNo, orderRefundNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ItemNo, itemNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCode);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCodeSup);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityType);
//            billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createDateTime, dateTimeFormatter));
//
//            billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, Total_Amount);
//            return billDTO;
//        } else {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_ORDER_PRICE_FOUND);
//        }
//    }
//
//    /**
//     * 根据退款单生成记账DTO
//     * @param saveOrderItem
//     * @param Total_Amount
//     * @param abstractTypeEnum
//     * @param relationTypeEnum
//     * @return
//     */
//    private AddToBillDTO generateDTOByOrderRefund(OrderItem saveOrderItem, OrderRefund orderRefund, BigDecimal Total_Amount, AbstractTypeEnum abstractTypeEnum, RelationTypeEnum relationTypeEnum) throws Exception {
//        Date createDateTime = saveOrderItem.getCreateTime();
//        Long orderItemId = saveOrderItem.getId();
//        Long orderRefundId = orderRefund.getId();
//        String orderRefundNo = orderRefund.getOrderRefundNo();
//
//        Orders orders = iOrdersService.queryById(saveOrderItem.getOrderId());
//        String orderNo = orders.getOrderNo();
//        String tenantId = saveOrderItem.getSupplierTenantId();
//
//        String activityCode = saveOrderItem.getActivityCode();
//        String activityType = EnumUtil.toString(saveOrderItem.getActivityType());
//        String itemNo = saveOrderItem.getProductSkuCode();
//
//        AddToBillDTO billDTO = AddToBillDTO.init(tenantId, Total_Amount, abstractTypeEnum, relationTypeEnum, orderRefundId);
//        log.info("记账 orderRefundId = {}, totalPriceSup = {}", orderRefundId, Total_Amount);
//        log.info("记账 orderRefundId = {}, activityCode = {}", orderRefundId, activityCode);
//
//        String activityCodeSup = null;
//        if (StrUtil.isNotBlank(activityCode)) {
//            ProductActivityItem activityPriceItem =
//                iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).build());
//            activityCodeSup = activityPriceItem.getActivityCodeParent();
//        }
//
//        if (Total_Amount != null) {
//            billDTO.addAbstractField(AbstractFieldTypeEnum.Total_RefundAmount, Total_Amount);
//
//            billDTO.addRelationField(RelationFieldTypeEnum.OrderNo, orderNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.OrderRefundNo, orderRefundNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ItemNo, itemNo);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCode, activityCode);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityCodeSup, activityCodeSup);
//            billDTO.addRelationField(RelationFieldTypeEnum.ActivityType, activityType);
//            billDTO.addRelationField(RelationFieldTypeEnum.CreateDateTime, DateUtil.format(createDateTime, dateTimeFormatter));
//
//            billDTO.addRelationField(RelationFieldTypeEnum.TotalAmount, Total_Amount);
//            return billDTO;
//        } else {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.BILLING_FAILURE_ORDER_PRICE_FOUND);
//        }
//    }

}
