package com.zsmall.system.entity.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/14 11:48
 */
@Data
public class TransactionReceiptDTO {
    /**
     * 资金流水号
     */
    private String serialId;
    private String tenantType;
    /**
     * 银行账号
     */
    private String bankAcc;

    /**
     * 帐户名称
     */
    private String accountName;
    /**
     * 实际交易时间
     */
    private Date tradeDate;
    /**
     * 币种
     */
    private String currency;
//    /**
//     * 交易类型（Recharge-充值，Withdrawal-提现） 方向
//     */
//    private TransactionTypeEnum transactionType;

    /**
     * 收支方向（0:收入 1:支出）
     */
    private Byte cdSign;

    /**
     * 收入金额
     */
    private BigDecimal incomeAmount;

    /**
     * 支出金额
     */
    private BigDecimal outlayAmount;

    /**
     * 交易金额
     */
//    private BigDecimal transactionAmount;

    /**
     * 分销店铺标识:付款方户名
     */
    private String oppAccName;

    /**
     * 分销店铺标识:付款方账号
     */
    private String oppAccNo;

    /**
     * 摘要
     */
    private String abs;

    /**
     * 账户结余
     */
    private BigDecimal accountBalance;

    /**
     * 子类型
     */
    private String subType;
}
