package com.zsmall.system.entity.domain.dto.settleInBasic;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/18 17:04
 */
@Data
public class UserSupReviewRecordParamDto {

    //账户名称
    private String accountName;
    //邮箱
    private String email;
    //用户编码
    private String TenantId;
    //电话号码
    private String phoneNumber;
    //审核状态
    private String reviewState;
    //开始时间
    private String startTime;
    //结束时间
    private String endTime;

    //日期
    private String dateTime;

    /**
     *  租户类型
     */
    private String tenantType;
}
