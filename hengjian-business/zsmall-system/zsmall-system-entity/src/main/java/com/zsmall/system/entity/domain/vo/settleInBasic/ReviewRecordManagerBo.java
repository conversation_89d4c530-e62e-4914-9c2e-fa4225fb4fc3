package com.zsmall.system.entity.domain.vo.settleInBasic;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 响应信息-供应商入驻公司联系人信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReviewRecordManagerBo {

    /**
     * 租户编码
     */
    private String tenantId;
    /**
     * 租户类型
     */
    private String tenantType;
    /**
     *账户名称
     */
    private String accountName;
    /**
     *状态
     */
    private String state;
    /**
     *国家
     */
    private String country;
    /**
     *电话号码
     */
    private String phoneNumber;
    /**
     *邮箱
     */
    private String email;
    /**
     *商品源类型
     */
    private String productSourceType;
    /**
     *国家_CN
     */
    private String countryCn;
    /**
     * 国家_EN
     */
    private String countryEn;
    /**
     *国家Id
     */
    private String countryId;
    /**
     *注册时间
     */
    private String registeredTime;
    /**
     *最后操作时间
     */
    private String lastOperationTime;

    /**
     * 拒绝原因
     */
    private String reviewReason;
}
