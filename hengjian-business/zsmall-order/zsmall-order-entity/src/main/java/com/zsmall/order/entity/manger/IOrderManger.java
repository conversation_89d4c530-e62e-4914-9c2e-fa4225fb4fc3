package com.zsmall.order.entity.manger;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.service.ISysApiService;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.dto.OpenApiOrderReviewDTO;
import com.zsmall.common.enums.order.ChannelReceiptStatusEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderRefundService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/11 16:01
 */
@Manager
@Slf4j
public class IOrderManger {
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private IOrderRefundManger iOrderRefundManger;

    @Resource
    private IWarehouseBizArkConfigService iWarehouseBizArkConfigService;

    @Resource
    private ISysApiService iSysApiService;

    @Resource
    private IOrderItemService iOrderItemService;
    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }


    /**
     * 功能描述：自动取消订单
     *
     * @param tenantId 分销商租户id
     * <AUTHOR>
     * @date 2025/03/11
     */
    public void cancelOrderAuto(String orderNo, String tenantId) {
        iOrdersService.cancelOrder(orderNo);
    }

    /**
     * 功能描述：同意退款,处理退款
     * 1 拦截成功
     * 0.拦截中
     * -1.拦截异常
     *
     * @param status        状态代码
     * @param orderExtendId 行id
     * @param tenantId      租户id
     * @param isAbnormal
     * <AUTHOR>
     * @date 2025/03/11
     */
//    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    @InMethodLog("自动取消流程")
    public void processRefundsAuto(Integer status, String orderExtendId, String tenantId, Boolean isAbnormal) {
        IOrderManger proxy = (IOrderManger) AopContext.currentProxy();
        if(status==1){
            SubmitRefundApplyBo submitRefundApplyBo = new SubmitRefundApplyBo();
            submitRefundApplyBo.setOrderNo(orderExtendId);
            submitRefundApplyBo.setTenantId(tenantId);
            submitRefundApplyBo.setIsAbnormal(isAbnormal);
            LoginUser loginUser = null;
            try{
                loginUser = LoginHelper.getLoginUser();

            }catch (Exception e){
                log.info("自动取消流程-未登陆");
            }

            if(ObjectUtil.isNotEmpty(loginUser)){
                // 提交退款,submitRefundApplyBo内有退款单号集合
                iOrderRefundManger.submitRefundApply(submitRefundApplyBo);
            }else {
                TenantHelper.ignore(()->iOrderRefundManger.submitRefundApply(submitRefundApplyBo));
            }
            // 退款通过 走事件
            proxy.refundSuccessProcess(orderExtendId, submitRefundApplyBo, true, false);

        }
        if(status==0){
            // 取消中
            return;
        }
        if(status==-1){
            TenantHelper.ignore(()->iOrdersService.cancelAbnormal(orderExtendId));
        }
        // 失败
        if(status==2||status==3){
            // fail 流程 变更状态为取消失败,仍然为待发货状态
            TenantHelper.ignore(()->iOrdersService.cancelFailed(orderExtendId));
        }
    }

    /**
     * 功能描述：
     * 功能描述：退款成功流程-包含修改订单状态为已成功
     * submitRefundApplyBo内的orderRefundNos需要完整订单的所有退款编号
     * 例如:LTL订单拆成了3单,相应的退款单会有3条,此处的orderRefundNos需要都塞入
     *
     * @param orderExtendId       订单扩展id
     * @param submitRefundApplyBo 提交退款申请bo
     * @param isAuto              是否自动退款
     * @param supplierCall        是否供应商发起
     * <AUTHOR>
     * @date 2025/06/27
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void refundSuccessProcess(String orderExtendId, SubmitRefundApplyBo submitRefundApplyBo, Boolean isAuto,
                                     Boolean supplierCall) {
        iOrderRefundManger.refundApplyHandle(submitRefundApplyBo.getOrderRefundNos(), isAuto, supplierCall, submitRefundApplyBo.getIsAbnormal());
        // 订单状态更新为已取消
        TenantHelper.ignore(()->iOrdersService.cancelSuccess(orderExtendId));
    }

    /**
     * 功能描述：供应商退款申请处理
     *
     * @param openApiOrderReviewDTO 开放api订单审核dto
     * @param tenantId              租户id
     * <AUTHOR>
     * @date 2025/07/14
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void supplierRefundApplyHandle(OpenApiOrderReviewDTO openApiOrderReviewDTO, String tenantId) {
        iOrderRefundManger.supplierRefundApplyHandle(openApiOrderReviewDTO,tenantId);

    }

    /**
     * 功能描述：流程退款不自动,仅提交退款
     *
     * @param orderExtendId 订单扩展id
     * @param tenantId      租户id
     * <AUTHOR>
     * @date 2025/03/13
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void processRefundsNotAuto(String orderExtendId, String tenantId) {
        SubmitRefundApplyBo submitRefundApplyBo = new SubmitRefundApplyBo();
        submitRefundApplyBo.setOrderNo(orderExtendId);
        submitRefundApplyBo.setTenantId(tenantId);
        iOrderRefundManger.submitRefundApply(submitRefundApplyBo);
    }

    /**
     * 功能描述：取消订单流
     *
     * @param nos              orderExtendId
     * @param tenantId         租户id
     * @param supplierTenantId 供应商租户id
     * <AUTHOR>
     * @date 2025/03/13
     */
    public void cancelOrderFlow(List<String> nos, String tenantId,String supplierTenantId) {
        IOrderManger proxy = (IOrderManger) AopContext.currentProxy();
        nos.forEach(orderExtendId -> {
            if (CollUtil.isEmpty(nos) || ObjectUtil.isEmpty(tenantId)) {
                throw new RuntimeException("请检查订单号和租户id");
            }
            RedissonClient client = RedisUtils.getClient();
            String lockKey = RedisConstants.CANCEL_ORDER_LOCK + ":" + orderExtendId;
            RLock rLock = client.getLock(lockKey);
            try {
                // 尝试立即获取锁，锁持有时间5分钟（根据业务调整）
                boolean isLockAcquired = rLock.tryLock(0, 10, TimeUnit.MINUTES);
                if (!isLockAcquired) {
                    throw new RuntimeException("订单正在处理中，请勿重复操作");
                }
                // 判断订单状态是否为 取消异常/待取消/0
                checkOrderStatus(orderExtendId);

                // 订单取消
                TenantHelper.ignore(()->cancelOrderAuto(orderExtendId, tenantId));
                // 判断分销商是否是sys_api支持的,不支持走手动退款,并且不调用erp接口
                // 自动+!ab(实际上没有发送到erp,肯定会取消失败) 自动+ab !自动+!ab !自动+ab
                boolean isNotExist = TenantHelper.ignore(()->iSysApiService.isNotExist(supplierTenantId));
                if (isNotExist&&(!isAbnormal(orderExtendId))) {
                    // 走手动,且不自动审批
                    proxy.processRefundsNotAuto(orderExtendId, tenantId);
                } else if(isNotExist && isAbnormal(orderExtendId)){
                    // 判断订单是否是abnormal,自动审批
                    proxy.processRefundsNotAuto(orderExtendId, tenantId);
                    OpenApiOrderReviewDTO openApiOrderReviewDTO = new OpenApiOrderReviewDTO();
                    openApiOrderReviewDTO.setTenantId(supplierTenantId);
                    openApiOrderReviewDTO.setOrderReviewOpinion(0);
                    openApiOrderReviewDTO.setOrderNo(orderExtendId);
                    // 通过事件调用
                    proxy.supplierRefundApplyHandle(openApiOrderReviewDTO,supplierTenantId);
                } else {
                    // 走自动
                    // 校验是否已经发送到erp,如果没有发送到erp则走直接取消
                    Boolean pushToErp = isPushToErp(orderExtendId);
                    boolean abnormal = isAbnormal(orderExtendId);
                    if(pushToErp){
                        ThebizarkDelegate delegate = initDelegate(supplierTenantId);
                        Result<Integer> result = delegate.orderApi().cancelOrderV2(orderExtendId);
                        if(checkCancelStatus(result)){
                            TenantHelper.ignore(() -> proxy.processRefundsAuto(result.getData(), orderExtendId, tenantId, abnormal));
                        }
                    }else {
                        TenantHelper.ignore(() -> proxy.processRefundsAuto(1, orderExtendId, tenantId, abnormal));
                    }

                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                // 确保当前线程持有锁时才释放（避免误释放其他线程的锁）
                if (rLock.isHeldByCurrentThread() && rLock.isLocked()) {
                    rLock.unlock();
                }
            }
        });


    }

    private Boolean isPushToErp(String orderExtendId) {
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        long count = orders.stream()
                               .filter(order -> !ChannelReceiptStatusEnum.SUCCESS.getDesc().equals(order.getChannelReceiptStatus()) )
                               .count();
        if(count>0){
            return false;
        }
        return true;
    }

    private boolean isAbnormal(String orderExtendId) {
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderExtendId));
        //  判断List<Orders> Orders的exceptionCode,如果是10则返回true,否则返回false
        long count = ordersList.stream()
                               .filter(order -> order.getExceptionCode() == 10)
                               .count();
        if(count==ordersList.size()){
            return true;
        }else {
            return false;
        }

    }

    /**
     * 功能描述：检查订单状态
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/03/27
     */
    public void checkOrderStatus(String orderExtendId) {
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderExtendId));
        // ordersList 的元素的cancelStatus 为1和2时，抛出异常
        ordersList.stream()
                  .filter(order -> order.getCancelStatus() == 1 || order.getCancelStatus() == 2)
                  .findFirst()
                  .ifPresent(order -> {
                      if (order.getCancelStatus() == 1) {
                          throw new RuntimeException("订单已在取消中，请勿重复取消");
                      } else {
                          throw new RuntimeException("订单已取消，请勿重复取消");
                      }
                  });
    }

    /**
     * 功能描述：检查取消状态
     *
     * @param result 后果
     * @return boolean
     * <AUTHOR>
     * @date 2025/03/20
     */
    private static boolean checkCancelStatus(Result<Integer> result) {
        return ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.getData());
    }

    /**
     * 功能描述：取消订单流补偿,业务场景:erp手动退款,已成功,再在分销取消退款,导致款项无法成功退款至钱包
     *
     * @param nos              orderExtendId
     * @param tenantId         租户id
     * @param supplierTenantId 供应商租户id
     * <AUTHOR>
     * @date 2025/03/13
     */
    public void cancelOrderFlowCompensation(List<String> nos, String tenantId,String supplierTenantId) {
        IOrderManger proxy = (IOrderManger) AopContext.currentProxy();
        nos.forEach(orderExtendId -> {
            if (CollUtil.isEmpty(nos) || ObjectUtil.isEmpty(tenantId)) {
                throw new RuntimeException("请检查订单号和租户id");
            }
            RedissonClient client = RedisUtils.getClient();
            String lockKey = RedisConstants.CANCEL_ORDER_LOCK + ":" + orderExtendId;
            RLock rLock = client.getLock(lockKey);
            try {
                // 尝试立即获取锁，锁持有时间5分钟（根据业务调整）
                boolean isLockAcquired = rLock.tryLock(0, 10, TimeUnit.MINUTES);
                if (!isLockAcquired) {
                    throw new RuntimeException("订单正在处理中，请勿重复操作");
                }
                // 判断订单状态是否为 取消异常/待取消/0
                checkOrderStatus(orderExtendId);

                // 订单取消
                TenantHelper.ignore(()->cancelOrderAuto(orderExtendId, tenantId));
                // 判断分销商是否是sys_api支持的,不支持走手动退款,并且不调用erp接口

                boolean isNotExist = TenantHelper.ignore(()->iSysApiService.isNotExist(supplierTenantId));
                if (isNotExist) {
                    // 走手动
                    proxy.processRefundsNotAuto(orderExtendId, tenantId);
                } else {
                    // 自动-默认成功
                    TenantHelper.ignore(()->((IOrderManger) AopContext.currentProxy()).processRefundsAuto(1,orderExtendId,tenantId, false));
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                // 确保当前线程持有锁时才释放（避免误释放其他线程的锁）
                if (rLock.isHeldByCurrentThread() && rLock.isLocked()) {
                    rLock.unlock();
                }
            }
        });
    }

    /**
     * 功能描述：取消订单流补偿前数据填充
     *
     * @param orderNos 订单号集合
     * <AUTHOR>
     * @date 2025/04/21
     */
    public void cancelOrderFlowCompensationAfter(List<String> orderNos) {
        for (String orderNo : orderNos) {
            try {
                Orders orders = iOrdersService.getByOrderNo(orderNo);
                LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
                q.eq(com.zsmall.order.entity.domain.OrderItem::getOrderId,orders.getId());
                com.zsmall.order.entity.domain.OrderItem orderItem =TenantHelper.ignore(()->iOrderItemService.getBaseMapper().selectOne(q));
                if (ObjectUtil.isNull(orderItem)){
                    throw new RuntimeException("订单明细不存在"+orders.getId());
                }
                cancelOrderFlowCompensation(ListUtil.of(orders.getOrderExtendId()),orderItem.getTenantId(),orderItem.getSupplierTenantId());
            }catch (Exception e){
                log.error("errorMsg",e);
            }

        }

    }

    /**
     * 功能描述：因未付款而取消订单
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/05/28
     */
    public void cancelOrderForNotPay(String orderExtendId) {
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        List<Orders> list = new ArrayList<>();
        for (Orders order : orders) {
            order.setOrderState(OrderStateType.Canceled);
            list.add(order);
        }
        TenantHelper.ignore(()->iOrdersService.updateBatchById(list));
    }
}
