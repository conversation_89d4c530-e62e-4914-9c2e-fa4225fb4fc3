<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-order</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-order-biz</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall订单业务模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-order-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.logistics</groupId>
            <artifactId>zsmall-logistics-tracking17</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-wayfair</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <version>2.4.12</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lianpay.globalpay</groupId>
            <artifactId>globalpay-sdk</artifactId>
            <version>0.0.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hengjian.openapi</groupId>
            <artifactId>hengjian-openapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-calculate-entity</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-price-entity</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-calculate-entity</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
