package com.zsmall.order.biz.manger;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.domain.ProductActivityPriceItem;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.orderImportRecord.ImportTypeEnum;
import com.zsmall.common.enums.orderImportRecord.OrderTempState;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.anno.annotaion.TempOrderLimit;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.orderImport.TempOrder2OrdersBo;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.vo.orderImport.TempOrder2OrdersVo;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/2 17:04
 */
@Manager
@Slf4j
public class IOrderImportRecordManger {
    @Resource
    private IWarehouseService iWarehouseService;

    @Resource
    private DeliveryFeeV2Utils deliveryFeeV2Utils;

    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private ProductSkuStockMapper productSkuStockMapper;
    @Resource
    private IOrderImportTempService iOrderImportTempService;
    @Resource
    private IOrderImportRecordService iOrderImportRecordService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private OrderSupport orderSupport;
    @Resource
    private IProductActivityPriceItemService iProductActivityPriceItemService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService ;

    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;

    @Resource
    private IUserShippingCartService iUserShippingCartService;
    @Resource
    private IProductSkuStockService iProductSkuStockService;
    public List<String> getStashList(String productSkuCode,Integer quantity) {

        LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
            .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
            .ge(ProductSkuStock::getStockAvailable, quantity)
            .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
            .eq(ProductSkuStock::getDelFlag, 0);
        List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
        List<String> stashCodes1 = null;
        if(CollUtil.isNotEmpty(list)){
            List<String> stashCodes = list.stream().map(ProductSkuStock::getWarehouseSystemCode).collect(Collectors.toList());
            LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, stashCodes);
            List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
            stashCodes1 = list1.stream().map(Warehouse::getWarehouseCode)
                               .collect(Collectors.toList());
        }else {
            LambdaQueryWrapper<ProductSkuStock> eq2 = new LambdaQueryWrapper<ProductSkuStock>()
                .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
                .eq(ProductSkuStock::getDelFlag, 0);
            List<ProductSkuStock> list2 = TenantHelper.ignore(()->iProductSkuStockService.list(eq2));
            List<String> stashCodes2 = null;
            if(CollUtil.isNotEmpty(list2)) {
                List<String> stashCodes = list2.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                               .collect(Collectors.toList());
                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>()
                    .in(Warehouse::getWarehouseSystemCode, stashCodes)
                    .orderByDesc(Warehouse::getCreateTime)
                    .orderByDesc(Warehouse::getId);
                List<Warehouse> list1 = TenantHelper.ignore(() -> iWarehouseService.list(in));
                stashCodes2 = list1.stream().map(Warehouse::getWarehouseCode)
                                   .collect(Collectors.toList());
                // todo 测算放开
//            throw new RuntimeException("库存不足");
                if(CollUtil.isEmpty(stashCodes2)){
                    throw new RuntimeException("库存不足");
                }
                return stashCodes2;
            }
        }
        if(CollUtil.isEmpty(stashCodes1)){
            throw new RuntimeException("库存不足");
        }
        return stashCodes1;
    }
    @TempOrderLimit(interval = 3, timeUnit = TimeUnit.SECONDS)
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<TempOrder2OrdersVo> tempOrder2Orders(TempOrder2OrdersBo bo) throws Exception {

        String recordNo = bo.getRecordNo();
        List<String> tempOrderNoList = bo.getTempOrderNoList();
        List<String> orderNoList = new ArrayList<>();

        BigDecimal allProductPrice = BigDecimal.ZERO;
        BigDecimal allShippingFee = BigDecimal.ZERO;
        BigDecimal allTotalPrice = BigDecimal.ZERO;
        if (CollUtil.isEmpty(tempOrderNoList) || StrUtil.isBlank(recordNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        OrderImportRecord orderImportRecord = iOrderImportRecordService.queryByRecordNo(recordNo);
        ImportTypeEnum importType = orderImportRecord.getImportType();

        List<OrderImportTemp> tempOrderList = iOrderImportTempService.queryPendingByTempOrderNoList(tempOrderNoList);
        if (CollUtil.isEmpty(tempOrderList)) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }

        for (var tempOrder : tempOrderList) {
            String orderNo = tempOrder.getOrderNo();
            String orderExtendId = tempOrder.getOrderExtendId();
            String storeName = tempOrder.getStoreName();
            String storeOrderId = tempOrder.getStoreOrderId();
            String productSkuCode = tempOrder.getProductSkuCode();
            Integer productQuantity = tempOrder.getProductQuantity();
            String recipientName = tempOrder.getRecipientName();
            String address1 = tempOrder.getAddress1();
            String address2 = tempOrder.getAddress2();
            String address3 = tempOrder.getAddress3();
            String companyName = tempOrder.getCompanyName();
            String city = tempOrder.getCity();
            String stateCode = tempOrder.getStateCode();
            String zipCode = tempOrder.getZipCode();
            String phoneNumber = tempOrder.getPhoneNumber();
            String countryCode = tempOrder.getCountryCode();
            LogisticsTypeEnum logisticsType = tempOrder.getLogisticsType();
            Long siteId = tempOrder.getSiteId();
            String currencyCode = tempOrder.getCurrencyCode();
            String currencySymbol = tempOrder.getCurrencySymbol();
            String logisticsCarrier = tempOrder.getLogisticsCarrier();
            Long bolOssId = tempOrder.getBolOssId();
            if(CarrierTypeEnum.LTL.name().equals(logisticsCarrier)){
                if (ObjectUtil.isEmpty(bolOssId)){
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.LTL_ORDER_BOL_NEED);
                }
            }

            if (StrUtil.isEmpty(productSkuCode)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_CODE_ISNULL_ERROR);
            }
            Product product = iProductService.queryByProductSkuCode(productSkuCode);
            if (ObjectUtil.isNull(product)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODCUT_NOT_FOUND_ERROR);
            }
            if ((ObjectUtil.equal(SupportedLogisticsEnum.PickUpOnly.name(),product.getSupportedLogistics().name())
                && ObjectUtil.notEqual(LogisticsTypeEnum.PickUp.name(), logisticsType.name()))) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.UNSUPPORTED_LOGISTICS_METHODS.args(logisticsType.name()));
            }
            if ((ObjectUtil.equal(SupportedLogisticsEnum.DropShippingOnly.name(),product.getSupportedLogistics().name())
                && ObjectUtil.notEqual(LogisticsTypeEnum.DropShipping.name(), logisticsType.name()))) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.UNSUPPORTED_LOGISTICS_METHODS.args(logisticsType.name()));
            }
            String warehouseSystemCode = tempOrder.getWarehouseSystemCode();

            String logisticsServiceName = tempOrder.getLogisticsServiceName();
            JSONArray logisticsTrackingNo = tempOrder.getLogisticsTrackingNo();
            Boolean logisticsThirdBilling = tempOrder.getLogisticsThirdBilling() == null ? false : tempOrder.getLogisticsThirdBilling();
            tempOrder.setLogisticsThirdBilling(logisticsThirdBilling);
            String channelTypeTemp = tempOrder.getChannelType();
            Integer orderSource = tempOrder.getOrderSource();
            BigDecimal dropShippingPrice = tempOrder.getDropShippingPrice();
            String logisticsAccount = tempOrder.getLogisticsAccount();
            String logisticsAccountZipCode = tempOrder.getLogisticsAccountZipCode();
            BigDecimal shippingFee = tempOrder.getShippingFee();
            Long shippingLabelOssId = tempOrder.getShippingLabelOssId();

            String shippingLabelFileName = tempOrder.getShippingLabelFileName();
            String activityCode = tempOrder.getActivityCode();
            Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);
            BigDecimal platformPickUpPrice = tempOrder.getPickUpPrice();
            BigDecimal platformDropShippingPrice = tempOrder.getDropShippingPrice();

            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                if (!logisticsThirdBilling && shippingLabelOssId == null) {
                    // 如果是ltl的单子,允许不给label
                    if(CarrierTypeEnum.LTL.getValue().equals(logisticsCarrier)){

                    }else {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                    }

                }

                if (!logisticsThirdBilling && CollUtil.isEmpty(logisticsTrackingNo)&&!CarrierTypeEnum.LTL.name().equals(logisticsCarrier)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_TRACKING_NO_BLANK_ERROR);
                }

                if (logisticsThirdBilling && StrUtil.isBlank(logisticsAccount)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR);
                }

                if (logisticsThirdBilling && StrUtil.isBlank(logisticsAccountZipCode)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR);
                }

                if (StrUtil.isBlank(logisticsCarrier)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SHIPPING_NAME_CAN_NOT_NULL);
                }
                if (StrUtil.isBlank(warehouseSystemCode)){
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_EXIST_ERROR);
                }
            }
            ChannelTypeEnum channelType;
            // 如果未指定渠道 走原逻辑
            if(StrUtil.isNotEmpty(channelTypeTemp)){
                // 拿字典表
                channelType = ChannelTypeEnum.getChannelTypeEnum(channelTypeTemp);
            }else{
                channelType = ObjUtil.equals(importType, ImportTypeEnum.ShippingCart) ?
                    ChannelTypeEnum.Marketplace : ChannelTypeEnum.Others;
            }
            // 规定渠道

            HashMap<String, Integer> codesMap = new HashMap<String, Integer>();
            // 开始生成订单
            Orders order = new Orders();
            order.setOrderNo(orderNo);
            order.setOrderExtendId(orderExtendId);
            order.setChannelAlias(storeName);
            order.setChannelOrderNo(storeOrderId);
            order.setChannelOrderName(storeOrderId);
            order.setChannelType(channelType);
            order.setSiteId(siteId);
            order.setCurrency(currencyCode);
            order.setCountryCode(countryCode);
            order.setCurrencySymbol(currencySymbol);
            order.setTotalQuantity(productQuantity);
            order.setOrderType(OrderType.Normal);
            order.setLogisticsType(logisticsType);
            order.setOrderState(OrderStateType.UnPaid);
            order.setFulfillmentProgress(LogisticsProgress.UnDispatched );
            order.setOrderSource(orderSource);
            iOrdersService.save(order);

            Long orderId = order.getId();

            OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();

            orderLogisticsInfo.setOrderId(orderId);
            orderLogisticsInfo.setOrderNo(orderNo);
            orderLogisticsInfo.setLogisticsType(logisticsType);
            orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
            orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);
            orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrier);
            orderLogisticsInfo.setLogisticsServiceName(logisticsServiceName);
            orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
            orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);
            if (shippingLabelOssId != null) {
                orderLogisticsInfo.setShippingLabelExist(true);
                orderLogisticsInfo.setShippingLabelOssId(shippingLabelOssId);
                orderLogisticsInfo.setShippingLabelFileName(shippingLabelFileName);
            }

            orderLogisticsInfo.setZipCode(StrUtil.trim(zipCode));
            orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zipCode));
            if (StrUtil.contains(zipCode, "-")) {
                // 存在-的邮编，需要分割出前面5位的主邮编
                String mainZipCode = StrUtil.trim(StrUtil.split(zipCode, "-").get(0));
                orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
            }
            orderLogisticsInfo.setLogisticsCountryCode(countryCode);

            orderAddressInfo.setOrderId(orderId);
            orderAddressInfo.setOrderNo(orderNo);
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            orderAddressInfo.setRecipient(recipientName);
            orderAddressInfo.setPhoneNumber(phoneNumber);
            orderAddressInfo.setCountry(countryCode);
            orderAddressInfo.setCountryCode(countryCode);
            orderAddressInfo.setState(stateCode);
            orderAddressInfo.setStateCode(stateCode);
            orderAddressInfo.setCity(city);
            orderAddressInfo.setAddress1(address1);
            orderAddressInfo.setAddress2(address2);
            orderAddressInfo.setAddress3(address3);
            orderAddressInfo.setCompanyName(companyName);
            orderAddressInfo.setZipCode(zipCode);

            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseSystemCode);
            if (productSku == null) {
                if (StrUtil.isNotBlank(warehouseSystemCode)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE.args(productSkuCode, warehouseSystemCode));
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_SOME_PRODUCT_NOT_EXIST.args(productSkuCode));
                }
            }

            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            paramDTO.setActivityCode(activityCode);
            paramDTO.setCountry(countryCode);
            paramDTO.setChannelTypeEnum(channelType);
            paramDTO.setLogisticsType(logisticsType);
            paramDTO.setLogisticsThirdBilling(logisticsThirdBilling);
            paramDTO.setWarehouseSystemCode(warehouseSystemCode);
            OrderItemDTO orderItemDTO = orderSupport.generateOrderItem(LoginHelper.getTenantId(), order, productSku, productQuantity, paramDTO, zipCode, logisticsCarrier);
            String logisticsCarrierCode = paramDTO.getLogisticsCarrierCode();
            if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrierCode);
                orderLogisticsInfo.setLogisticsCarrierCode(logisticsCarrierCode);
                orderLogisticsInfo.setLogisticsServiceName(paramDTO.getLogisticsCode());
            }
            OrderItem orderItem = orderItemDTO.getOrderItem();
            OrderItemPrice orderItemPrice = orderItemDTO.getOrderItemPrice();
            OrderItemProductSku orderItemProductSku = orderItemDTO.getOrderItemProductSku();
            LocaleMessage localeMessage = orderItemDTO.getLocaleMessage();

            if (localeMessage.hasData()) {
                orderLogisticsInfo.setLogisticsErrorMessage(localeMessage.toJSON());
            }

            // 处理物流信息
            List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
            if (LogisticsTypeEnum.PickUp.equals(logisticsType) && !logisticsThirdBilling) {
            // todo ltl 会有tracking为空的场景,为空就创建一个trackingNo为空的记录
                if(ObjectUtil.isNotEmpty(logisticsTrackingNo)){
                    for (int i = 0; i < logisticsTrackingNo.size(); i++) {
                        String trackingNo = logisticsTrackingNo.getStr(i);
                        String trimTracking = StrUtil.trim(trackingNo);
                        if (StrUtil.isNotBlank(trimTracking)) {
                            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
                            trackingRecord.setSku(productSku.getSku());
                            trackingRecord.setProductSkuCode(productSkuCode);
                            if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                                trackingRecord.setLogisticsCarrier(logisticsCarrierCode);
                            }else {
                                trackingRecord.setLogisticsCarrier(logisticsCarrier);
                            }

                            trackingRecord.setLogisticsService(logisticsServiceName);
                            trackingRecord.setLogisticsTrackingNo(trimTracking);
                            trackingRecord.setOrderNo(orderNo);
                            trackingRecord.setOrderItemNo(orderItem.getOrderItemNo());
                            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
                            trackingRecord.setWarehouseSystemCode(warehouseSystemCode);

                            if (CollUtil.size(logisticsTrackingNo) == productQuantity) {
                                trackingRecord.setQuantity(1);
                            } else {
                                trackingRecord.setQuantity(productQuantity);
                            }
                            trackingList.add(trackingRecord);
                        }
                    }
                }else {
                    OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
                    trackingRecord.setSku(productSku.getSku());
                    trackingRecord.setProductSkuCode(productSkuCode);
                    if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                        trackingRecord.setLogisticsCarrier(logisticsCarrierCode);
                    }else {
                        trackingRecord.setLogisticsCarrier(logisticsCarrier);
                    }

                    trackingRecord.setLogisticsService(logisticsServiceName);
                    trackingRecord.setOrderNo(orderNo);
                    trackingRecord.setOrderItemNo(orderItem.getOrderItemNo());
                    trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
                    trackingRecord.setWarehouseSystemCode(warehouseSystemCode);

                    if (CollUtil.size(logisticsTrackingNo) == productQuantity) {
                        trackingRecord.setQuantity(1);
                    } else {
                        trackingRecord.setQuantity(productQuantity);
                    }
                    trackingList.add(trackingRecord);
                }

            }


            if (StrUtil.isNotBlank(activityCode)) {  // 活动订单，需要查询活动价格
                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCode(activityCode);
                // 因为分销商参加活动时已经支付了定金，所以此处不能直接取自提价/代发价，只能取尾款单价
                platformPickUpPrice = activityPriceItem.getPlatformBalanceUnitPrice();
                // 代发价等于尾款单价加上尾程派送费
                platformDropShippingPrice = NumberUtil.add(platformPickUpPrice, activityPriceItem.getPlatformFinalDeliveryFee());
            }
            priceSupportV2.recalculateOrderAmount(order, CollUtil.newArrayList(orderItemPrice));
            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                codesMap.put(order.getOrderNo(),order.getExceptionCode());
            }
            if(ObjectUtil.isEmpty(shippingFee)){
                shippingFee = order.getPlatformTotalFinalDeliveryFee().divide(BigDecimal.valueOf(order.getTotalQuantity()),2, RoundingMode.HALF_UP);
                tempOrder.setShippingFee(shippingFee);
                tempOrder.setDropShippingTotalAmount(order.getPlatformTotalDropShippingPrice());
                tempOrder.setShippingTotalAmount(order.getPlatformTotalFinalDeliveryFee());
                tempOrder.setDropShippingPrice(order.getPlatformTotalDropShippingPrice().divide(BigDecimal.valueOf(order.getTotalQuantity()),2, RoundingMode.HALF_UP));
            }
//            iOrdersService.updateById(order);
            iOrdersService.updateBatchOrSetNull(Collections.singletonList(order),codesMap);
            iOrderLogisticsInfoService.save(orderLogisticsInfo);
            iOrderAddressInfoService.save(orderAddressInfo);

            orderItem.setOrderId(orderId);
            iOrderItemService.save(orderItem);
            Long orderItemId = orderItem.getId();

            orderItemPrice.setOrderItemId(orderItemId);
            iOrderItemPriceService.save(orderItemPrice);

            orderItemProductSku.setOrderItemId(orderItemId);
            iOrderItemProductSkuService.save(orderItemProductSku);

            iOrderItemTrackingRecordService.saveBatch(trackingList);
            tempOrder.setLogisticsCarrier(paramDTO.getLogisticsCarrierCode());
            tempOrder.setLogisticsCarrierCode(paramDTO.getLogisticsCode());
            tempOrder.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
            tempOrder.setLogisticsServiceName(paramDTO.getLogisticsCode());
            tempOrder.setPickUpTotalAmount(NumberUtil.mul(platformPickUpPrice, productQuantity));

            tempOrder.setTempState(OrderTempState.Official);

            orderNoList.add(orderNo);
            BigDecimal platformActualTotalAmount = order.getPlatformActualTotalAmount();
            if(ObjectUtil.isNotEmpty(platformActualTotalAmount)){
                allProductPrice = allProductPrice.add(platformActualTotalAmount);
                allTotalPrice = allTotalPrice.add(order.getPlatformActualTotalAmount());
            }

            if (ObjUtil.equals(importType, ImportTypeEnum.ShippingCart)) {
                if (ObjectUtil.equal(logisticsType.name(),LogisticsTypeEnum.PickUp.name())) {
                    iUserShippingCartService.deleteByProductSkuCode(productSkuCode,SupportedLogisticsEnum.PickUpOnly.name());
                }
                if (ObjectUtil.equal(logisticsType.name(),LogisticsTypeEnum.DropShipping.name())) {
                    iUserShippingCartService.deleteByProductSkuCode(productSkuCode,SupportedLogisticsEnum.DropShippingOnly.name());
                }
            }
        }

        iOrderImportTempService.updateBatchById(tempOrderList);

        orderImportRecord.setImportState(ImportStateEnum.Success);
        orderImportRecord.setImportOrders(CollUtil.size(tempOrderList));
        iOrderImportRecordService.updateById(orderImportRecord);
        TempOrder2OrdersVo vo = new TempOrder2OrdersVo();
        vo.setOrderNoList(orderNoList);
        vo.setAllProductPrice(allProductPrice);
        vo.setAllShippingFee(allShippingFee);
        vo.setAllTotalPrice(allProductPrice);
        return R.ok(vo);
    }
}
