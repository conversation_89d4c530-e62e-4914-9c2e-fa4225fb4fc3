package com.zsmall.order.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.JSONObjectUtils;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadBatchEvent;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.SysDictData;
import com.hengjian.system.domain.vo.BatchUploadFile;
import com.hengjian.system.domain.vo.SysDictDataVo;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysDictDataMapper;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysOssService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.domain.ProductActivityPriceItem;
import com.zsmall.activity.entity.domain.vo.productActivity.ActivityItemSimpleVo;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.support.DeliveryFeeSupport;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.orderImportRecord.ImportTypeEnum;
import com.zsmall.common.enums.orderImportRecord.OrderTempState;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productActivity.ProductActivityItemStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.worldLocation.LocationTypeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.exception.ShippingValidationException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.*;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.manger.IOrderImportRecordManger;
import com.zsmall.order.biz.service.OrderImportRecordService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.Tracking17Support;
import com.zsmall.order.entity.anno.annotaion.TempOrderLimit;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.orderImport.*;
import com.zsmall.order.entity.domain.dto.OrderImportDTO;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.vo.orderImport.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseDeliveryCountryService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单导入记录-业务实现
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrderImportRecordServiceImpl implements OrderImportRecordService {
    private final IWarehouseDeliveryCountryService iWarehouseDeliveryCountryService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final ISysConfigService sysConfigService;
    private final DeliveryFeeSupport deliveryFeeSupport;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final IProductSkuStockService iProductSkuStockService;
    private final ISysTenantService sysTenantService;
    private final PriceSupportV2 priceSupportV2;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrderImportRecordManger iOrderImportRecordManger;
    private final IOrderImportRecordService iOrderImportRecordService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IWarehouseService iWarehouseService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final ITracking17CarrierService iTracking17CarrierService;
    private final IWorldLocationService iWorldLocationService;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderImportTempService iOrderImportTempService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductChannelControlService iProductChannelControlService;
    private final IUserShippingCartService iUserShippingCartService;
    private final ISysOssService iSysOssService;


    private final Tracking17Support tracking17Support;
    private final OrderSupport orderSupport;

    private final BusinessParameterService businessParameterService;
    private final SysDictDataMapper sysDictDataMapper;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final SysDictDataMapper dictDataMapper;
    private final OrderCodeGeneratorV2 orderCodeGeneratorV2;

    /**
     * 分页查询查询订单导入记录列表
     *
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<OrderImportRecordVo> queryPageList(OrderImportRecordBo bo, PageQuery pageQuery) {
        return iOrderImportRecordService.queryPageList(pageQuery, bo.getQueryValue());
    }

    /**
     * 上传订单Excel
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<OrderImportRecordVo> uploadExcel(MultipartFile file) throws Exception {
//        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
//        String dtenantId = "DLTA1X9";
        String dtenantId = LoginHelper.getTenantId();
        String channelFlag = TenantHelper.ignore(() -> productSkuStockMapper.getChannelFlag(dtenantId));
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }
        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        OrderImportRecord record = new OrderImportRecord();
        record.setImportRecordNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderImportRecordNo));
        record.setImportFileName(file.getOriginalFilename());
        record.setImportType(ImportTypeEnum.Excel);
        record.setImportState(ImportStateEnum.Pending);
        record.setOrderTempState(0);
        Map<String, SiteCountryCurrency> siteMap = iSiteCountryCurrencyService.getSiteMap();
//        稍后放开
        iOrderImportRecordService.save(record);
        List<SysDictDataVo> shipping = dictDataMapper.selectDictDataByType("shipping");
        // shipping转为map,key是dictLabel,value是SysDictDataVo
        Map<String, SysDictDataVo> shippingMap = shipping.stream()
                                                         .collect(Collectors.toMap(SysDictDataVo::getDictLabel, Function.identity()));
        // shipping 取出所有元素的dictLabel,以逗号拼接转换成字符串
        String commaSeparatedString = shipping.stream()
                                              .filter(sysDictDataVo -> sysDictDataVo.getDictLabel() != null && !sysDictDataVo.getDictValue().equals("any")) // filter out null and any dictValue
                                              .map(SysDictDataVo::getDictLabel) // map each SysDictDataVo to its dictLabel
                                              .collect(Collectors.joining(", "));
//        shipping 取出所有元素的dict_value 放入List内,过滤掉any和AMSP;
        List<String> walmartCarriers = shipping.stream()
                                               .map(SysDictDataVo::getDictValue) // map each SysDictDataVo to its dictValue
                                               .filter(value -> !value.equals("any") && !value.equals("AMSP")) // filter out "any" and "AMSP"
                                               .collect(Collectors.toList()); // collect the result into a new list
        List<String> notWalmartCarriers = shipping.stream()
                                                  .map(SysDictDataVo::getDictValue) // map each SysDictDataVo to its dictValue
                                                  .filter(value -> !value.equals("any")) // filter out "any" and "AMSP"
                                                  .collect(Collectors.toList());
        try {
            String importRecordNo = record.getImportRecordNo();
            Long recordId = record.getId();
            List<OrderImportTemp> tempList = new ArrayList<>();
            List<String> currencyList = new ArrayList<>();

            if (reader != null) {
                Sheet sheet = reader.getSheet();
                String sheetName = sheet.getSheetName();
                log.info("uploadOrderExcel - sheetName = {}", sheetName);
                int columnCount = reader.getColumnCount();
                log.info("uploadOrderExcel - columnCount = {}", columnCount);
                // 原22 dev_1.0.1 减去了①第三方物流账户 ②，承运商账户 ③承运商账户邮编
                if (columnCount != 22) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
                }
                String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
                // 支持的国家
                List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
                // 支持的州
                List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");
                // 渠道单号重复Set
                Set<String> channelOrderNoSet = new HashSet<>();

                List<OrderImportDTO> orderImportDTOS;
                try {
                    orderImportDTOS = ZExcelUtil.parseFieldDTO(reader, OrderImportDTO.class, 11, 12);
                } catch (ExcelMessageException e) {
                    OrderImportRecordVo vo = new OrderImportRecordVo();
                    vo.setImportRecordNo(importRecordNo);
                    vo.setImportState(ImportStateEnum.Failed.name());
                    record.setImportMessage(e.getLocaleMessage().toJSON());
                    record.setImportState(ImportStateEnum.Failed);
                    iOrderImportRecordService.updateById(record);
                    return R.ok(vo);
                }

                if (CollUtil.isNotEmpty(orderImportDTOS)) {
                    log.info("客户:{},时间:{},导单原始数据 = {}",dtenantId,DateUtil.formatDateTime(new Date()) ,JSONUtil.toJsonStr(orderImportDTOS));
                    LocaleMessage localeMessage = new LocaleMessage();

                    ExcelMsgBuilder<OrderImportDTO> builder = ZExcelUtil.msgBuilder(reader, 11, OrderImportDTO.class);
                    builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");
                    // 校验阶段数据结构
                    Map<String, String> baseOrderExtendIdMap = new ConcurrentHashMap<>();  // channelOrderNo -> 基础orderExtendId
                    Map<String, Boolean> duplicateLtlMap = new ConcurrentHashMap<>();       // 标记重复的channelOrderNo
                    Map<String, AtomicInteger> sequenceMap = new ConcurrentHashMap<>();     // 重复渠道的序列号计数器
                    for (OrderImportDTO orderImportDTO : orderImportDTOS) {
                        //代发模式，不用客户指定的仓库
                        if(StrUtil.equals(orderImportDTO.getLogisticsType(), "DropShipping")) {
                            orderImportDTO.setWarehouseSystemCode(null);
                        }
                        String sequence = orderImportDTO.getSequence();
                        int showRowIndex = orderImportDTO.getShowRowIndex();
                        builder.setNowShowRow(showRowIndex);

                        if (StrUtil.isBlank(sequence)) {
                            continue;
                        }

                        String channelOrderNo = orderImportDTO.getChannelOrderNo();
                        String productSkuCode = orderImportDTO.getProductSkuCode();
                        String activityCode = orderImportDTO.getActivityCode();
                        String warehouseSystemCode = orderImportDTO.getWarehouseSystemCode();
                        String logisticsTrackingNo = orderImportDTO.getLogisticsTrackingNo();
                        Integer quantity = orderImportDTO.getQuantity();
                        String phoneNumber = orderImportDTO.getPhoneNumber();
                        String countryCode = orderImportDTO.getCountryCode();
                        String stateCode = orderImportDTO.getStateCode();
                        String zipCode = orderImportDTO.getZipCode();
                        String logisticsType = orderImportDTO.getLogisticsType();
                        String carrier = orderImportDTO.getLogisticsCarrier();
                        String channelType = orderImportDTO.getChannelType();
                        // todo要检查是否有不同的站点,有就报错


                        SiteCountryCurrency currency = new SiteCountryCurrency();
                        try {
                            currency = siteMap.get(countryCode);
                            if(ObjectUtil.isEmpty(currency)){
                                throw new Exception("获取站点信息失败,异常站点:"+countryCode);
                            }
                        }catch (Exception e){
                            log.error("获取站点信息失败",e);
                            throw new Exception("获取站点信息失败,异常站点:"+countryCode);
                        }




                        // todo currencyCode放入currencyList里,然后判断是否有不一样的币种,如果有重复数据抛出异常
                        String currencyCode = currency.getCurrencyCode();
                        currencyList.add(currencyCode);

                        // 转换成下
                        if (CollUtil.isEmpty(shipping)) {
                            localeMessage.append(builder.build(OrderImportDTO::getLogisticsCarrier, ExcelMessageEnum.CARRIER_CONFIGURATION_ABNORMAL));
                        }
                        if (!shippingMap.containsKey(carrier)) {
                            localeMessage.append(builder.build(OrderImportDTO::getLogisticsCarrier, ExcelMessageEnum.NON_VALID_CARRIER), commaSeparatedString);
                        } else {
                            if (ChannelTypeEnum.Walmart.name().equalsIgnoreCase(channelType)) {
                                if (CarrierTypeEnum.AMSP.getValue().equalsIgnoreCase(carrier)) {
                                    localeMessage.append(builder.build(OrderImportDTO::getLogisticsCarrier, ExcelMessageEnum.WALMART_CHANNEL_NOT_SUPPORT_AMSP));
                                }
                            }

                        }

                        LambdaQueryWrapper<SysDictData> eq = new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, "biz_channel_type");
                        List<SysDictData> sysDictData = sysDictDataMapper.selectList(eq);
                        if(CollUtil.isEmpty(sysDictData)||sysDictData.contains(channelType)){
                            localeMessage.append(builder.build(OrderImportDTO::getChannelType, ExcelMessageEnum.CHANNEL_NOT_EXIST));
                        }

//                        String thirdBilling = orderImportDTO.getThirdBilling();
//                        String carrierAccount = orderImportDTO.getCarrierAccount();
//                        String carrierAccountZipCode = orderImportDTO.getCarrierAccountZipCode();

                        if (StrUtil.contains(logisticsTrackingNo, "\n")) {
                            logisticsTrackingNo = StrUtil.replace(logisticsTrackingNo, "\n", "");
                            orderImportDTO.setLogisticsTrackingNo(logisticsTrackingNo);
                        }
                        if (StrUtil.contains(logisticsTrackingNo, "；")) {
                            logisticsTrackingNo = StrUtil.replace(logisticsTrackingNo, "；", ";");
                            orderImportDTO.setLogisticsTrackingNo(logisticsTrackingNo);
                        }



                        ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, null);
                        SiteCountryCurrency finalCurrency = currency;
                        ProductSkuPrice skuPrice = TenantHelper.ignore(()->iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode, finalCurrency.getId()));
                        if(ObjectUtil.isEmpty(skuPrice)){
                            localeMessage.append(builder.build(OrderImportDTO::getCountryCode, ExcelMessageEnum.SKU_ID_PRICE_SITE_PRICE_NOT_EXIST.args(productSkuCode)));
                        }
                        if (productSku == null) {
                            // localeMessage.appendSurround(ExcelMessageEnum.SKU_NOT_EXIST.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                            localeMessage.append(builder.build(OrderImportDTO::getProductSkuCode, ExcelMessageEnum.SKU_NOT_EXIST));
                            continue;
                        } else {
                            // 如果商品被管控，则报商品不存在
//                            boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, productSkuCode, ChannelTypeEnum.Others.name());
                            boolean checkUserAllow = iProductChannelControlService.checkUserAllow(dtenantId, productSkuCode, ChannelTypeEnum.Others.name());
                            if (!checkUserAllow) {
                                // localeMessage.appendSurround(ExcelMessageEnum.ITEM_NO_NOT_EXIST.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                localeMessage.append(builder.build(OrderImportDTO::getProductSkuCode, ExcelMessageEnum.ITEM_NO_NOT_EXIST));
                            }

                        }
                        //校验自提订单
                        if ("Pick Up".equals(logisticsType)){
//                            if (StrUtil.isBlank(warehouseSystemCode)){
//                           //     localeMessage.append(builder.build(OrderImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_SYSTEM_CODE_NOT_EXIST),"自提订单仓库ID不能为空");
//                                List<WarehouseVo> inStockWarehouseBySku = iProductSkuService.getInStockWarehouseBySku(productSku.getProductSkuCode(), 1, null);
//                                if (CollUtil.isNotEmpty(inStockWarehouseBySku)){
//                                    warehouseSystemCode=inStockWarehouseBySku.get(0).getWarehouseCode();
//                                    orderImportDTO.setWarehouseSystemCode(inStockWarehouseBySku.get(0).getWarehouseCode());
//                                }else {
//                                    localeMessage.append(builder.build(OrderImportDTO::getProductSkuCode, ExcelMessageEnum.WAREHOUSE_SYSTEM_CODE_NOT_EXIST),"当前商品所有仓库无库存");
//                                   // throw new RuntimeException(StrUtil.format("当前商品所有仓库无库存：{}", productSku.getProductSkuCode()));
//                                }
//                            }
                            if (StrUtil.isEmpty(warehouseSystemCode)){
                                localeMessage.append(builder.build(OrderImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_SYSTEM_CODE_NOT_EXIST),"自提订单仓库ID不能为空");
                            }
                            if (StrUtil.isBlank(carrier)){
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsCarrier, ExcelMessageEnum.LOGISTICS_CARRIER_NOT_EXIST),"自提订单承运商不能为空");
                            }
                            if (StrUtil.isBlank(logisticsTrackingNo)&&!"LTL".equals(carrier)){
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsTrackingNo, ExcelMessageEnum.LOGISTICS_TRACKINGNO_NOT_EXIST),"自提订单运单号不能为空");
                            }

                        }

                        if (ObjectUtil.isNotNull(warehouseSystemCode)){
                            //校验仓库的合法性
                            LambdaQueryWrapper<Warehouse> wa = new LambdaQueryWrapper<>();
                            wa.eq(Warehouse::getWarehouseState,1);
                            wa.eq(Warehouse::getWarehouseCode,warehouseSystemCode);
                            wa.eq(Warehouse::getTenantId,productSku.getTenantId());
                            Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.getOne(wa));
                            if (ObjectUtil.isNull(warehouse)){
                                localeMessage.append(builder.build(OrderImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND),"仓库编码不存在/已停用");
                            }else {
                                warehouseSystemCode=warehouse.getWarehouseSystemCode();
                                orderImportDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                            }
                        }
                        // 处理仓库code
                        if (StrUtil.isBlank(warehouseSystemCode)) {
                            warehouseSystemCode = null;
                            orderImportDTO.setWarehouseSystemCode(warehouseSystemCode);
                        }
                        Product product = iProductService.queryByProductSkuCode(productSkuCode);

                        // 渠道订单号不能重复 todo 记录重复的订单号,重复的订单号需要拼接-1 -2
                        if (StrUtil.isNotBlank(channelOrderNo)) {
                            if("LTL".equals(carrier)){
                                baseOrderExtendIdMap.computeIfAbsent(channelOrderNo, k ->
                                    orderCodeGeneratorV2.generateOrderNumber(BusinessCodeEnum.NewOrderNo)
                                );
                                // 标记重复状态（若已存在则返回true）
                                duplicateLtlMap.compute(channelOrderNo, (k, v) -> v != null);
                            }

                            if (channelOrderNoSet.contains(channelOrderNo)) {
                                if("LTL".equals(carrier)){

                                }else {
                                    localeMessage.append(builder.build(OrderImportDTO::getChannelOrderNo, ExcelMessageEnum.REPEAT_STORE_ORDER_ID));
                                }


                            } else {
                                // 查询此账户所有订单判断是否有重复的，排除Canceled的
                                Integer orderCount = iOrdersService.existsChannelOrderNoAndOrderStateAndIsShow(channelOrderNo, OrderStateType.Canceled,Boolean.FALSE);
                                // 查询导入缓存表
                                boolean tempOrderExists = iOrderImportTempService.existsChannelOrderNoAndTempState(channelOrderNo,OrderTempState.Temporary);
                                if(orderCount > 0){
                                    localeMessage.append(builder.build(OrderImportDTO::getChannelOrderNo, ExcelMessageEnum.REPEAT_STORE_ORDER_ID));
                                } else if (tempOrderExists){
                                    localeMessage.append(builder.build(OrderImportDTO::getChannelOrderNo, ExcelMessageEnum.REPEAT_STORE_ORDER_ID));
                                }else {
                                    channelOrderNoSet.add(channelOrderNo);
                                }
                            }
                        }else {
                            // 如果订单的渠道是LTL则报错
                            if("LTL".equals(carrier)){
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsTrackingNo, ExcelMessageEnum.CHANNEL_ORDER_NUMBER_CANNOT_BE_EMPTY));
                            }
                        }

                        // 规则校验
                        if (!RegexUtil.matchQuantity(quantity.toString())) {
                            localeMessage.appendSurround(ExcelMessageEnum.IRREGULARITY.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                            localeMessage.append(builder.build(OrderImportDTO::getQuantity, ExcelMessageEnum.IRREGULARITY));
                        }

                        // 校验手机号
                        if (StrUtil.isNotBlank(phoneNumber) && StrUtil.length(phoneNumber) > 80) {
                            // localeMessage.appendSurround(ExcelMessageEnum.PHONE_NUMBER_LENGTH.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                            localeMessage.append(builder.build(OrderImportDTO::getPhoneNumber, ExcelMessageEnum.PHONE_NUMBER_LENGTH));
                        }

                        // 校验国家 2022-8-16新需求，放开国家校验
                        if (StrUtil.equals(countryCode, "US")) {
                            // 校验州 2022-8-16新需求，仅US的才校验州
                            if (!SUPPORT_STATE.contains(stateCode)) {
                                // localeMessage.appendSurround(ExcelMessageEnum.STATE.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                localeMessage.append(builder.build(OrderImportDTO::getStateCode, ExcelMessageEnum.STATE));
                            }
                            // 校验邮编 2022-8-16新需求，仅US的才校验邮编
                            if (!RegexUtil.matchUSPostalCode(zipCode)) {
                                // localeMessage.appendSurround(ExcelMessageEnum.ZIP_CODE.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                localeMessage.append(builder.build(OrderImportDTO::getZipCode, ExcelMessageEnum.ZIP_CODE));
                            }
                        } else if (!SUPPORT_COUNTRY.contains(countryCode)) {
                            // localeMessage.appendSurround(ExcelMessageEnum.COUNTRY.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                            localeMessage.append(builder.build(OrderImportDTO::getCountryCode, ExcelMessageEnum.COUNTRY));
                        }

                        if (StrUtil.isNotBlank(activityCode)) {
                            ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(
                                ProductActivityItem.builder()
                                    .activityCode(activityCode)
                                    .activityState(ProductActivityItemStateEnum.InProgress)
                                    .productSkuCode(productSkuCode).build()
                            );

                            if (ObjectUtil.isNull(activityItem)) {
                                activityItem = iProductActivityItemService.queryOneByEntity(
                                    ProductActivityItem.builder()
                                        .activityCode(activityCode)
                                        .activityState(ProductActivityItemStateEnum.InProgress).build()
                                );

                                if (ObjectUtil.isNull(activityItem)) {
                                    // localeMessage.appendSurround(ExcelMessageEnum.ITEM_NO_NOT_JOIN_ACTIVITY.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                    localeMessage.append(builder.build(OrderImportDTO::getActivityCode, ExcelMessageEnum.ITEM_NO_NOT_JOIN_ACTIVITY));
                                } else {
                                    // localeMessage.appendSurround(ExcelMessageEnum.ACTIVITY_NOT_EXIST.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                    localeMessage.append(builder.build(OrderImportDTO::getActivityCode, ExcelMessageEnum.ITEM_NO_NOT_JOIN_ACTIVITY));
                                }
                            }
                        }

                        // 校验Shipping是否符合规范
                        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

                        if (StrUtil.equals(logisticsType, "Pick Up")) {
                            // 仅支持代发
                            if (SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {

                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsType, ExcelMessageEnum.DROP_SHIPPING_ONLY));
                            }

                            // 自提必填承运商
                            if (StrUtil.isBlank(carrier)) {
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsCarrier, ExcelMessageEnum.PICK_UP_CARRIER));
                            }

                        } else if (StrUtil.equals(logisticsType, "DropShipping")) {
                            // 仅支持自提
                            if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsType, ExcelMessageEnum.PICK_UP_ONLY));
                            }

                        } else {
                            // localeMessage.appendSurround(ExcelMessageEnum.SHIPPING_TYPE.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                            localeMessage.append(builder.build(OrderImportDTO::getLogisticsType, ExcelMessageEnum.SHIPPING_TYPE));
                        }

                        // 非活动商品需要判断是否绑定价格公式
                        if (StrUtil.isBlank(activityCode)) {
                            ProductSkuPriceRuleRelation relation = iProductSkuPriceRuleRelationService.getByProductSkuCode(productSkuCode);
                            if (relation == null) {
                                // localeMessage.appendSurround(ExcelMessageEnum.PRICE_RULE_NO_EXIST.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                localeMessage.append(builder.buildOnlyRow(ExcelMessageEnum.PRICE_RULE_NO_EXIST));
                            }
                        }

                        if (StrUtil.isNotBlank(logisticsTrackingNo)) {
                            // 物流单数量必须为一或者与商品数量一致
                            List<String> trackingNoList = StrUtil.split(logisticsTrackingNo, ";", true, true);
                            String oneTrackingNo = trackingNoList.get(0);
                            if (StrUtil.length(oneTrackingNo) > 22) {
                                // localeMessage.appendSurround(ExcelMessageEnum.TRACKING_FORMAT.buildLocalMessage(showRowIndex, null), "<p>", "</p></br>");
                                localeMessage.append(builder.build(OrderImportDTO::getLogisticsTrackingNo, ExcelMessageEnum.TRACKING_FORMAT));
                            }
                        }
                        if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
                            LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode)
                                                                                                       .eq(Warehouse::getDelFlag, 0);
                            Warehouse warehouse = TenantHelper.ignore(()->iWarehouseService.getOne(wrapper));
                            if(ObjectUtil.isNull(warehouse)){
                                localeMessage.append(builder.build(OrderImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
                            }
                        }
                    }
                    Set<String> uniqueCurrencySymbols = new HashSet<>();
                    currencyList.stream()
                                .forEach(currencySymbol -> {
                                    // Step 3: Check if the currency symbol is already in the set
                                    uniqueCurrencySymbols.add(currencySymbol);
                                });
                    if (uniqueCurrencySymbols.size() > 1) {
                        localeMessage.append(builder.build(ExcelMessageEnum.MULTIPLE_CURRENCIES_ARE_NOT_SUPPORTED));
                    }
                    OrderImportRecordVo vo = new OrderImportRecordVo();
                    vo.setImportRecordNo(importRecordNo);
                    if (localeMessage.hasData()) {
                        vo.setImportState(ImportStateEnum.Failed.name());
                        record.setImportMessage(localeMessage.toJSON());
                        record.setImportState(ImportStateEnum.Failed);
                        iOrderImportRecordService.updateById(record);
                        return R.ok(vo);
                    } else {  // 第一遍检查没有错误，开始生成临时订单

                        // 维护渠道编号到序列号的映射

                        for (OrderImportDTO orderImportDTO : orderImportDTOS) {
                            if (StrUtil.isBlank(orderImportDTO.getSequence())) {
                                continue;
                            }

                            String channelAlias = orderImportDTO.getChannelAlias();
                            String channelOrderNo = orderImportDTO.getChannelOrderNo();
                            String productSkuCode = orderImportDTO.getProductSkuCode();
                            String activityCode = orderImportDTO.getActivityCode();
                            String warehouseSystemCode = orderImportDTO.getWarehouseSystemCode();
                            Integer quantity = orderImportDTO.getQuantity();
                            String recipientName = orderImportDTO.getRecipientName();
                            String phoneNumber = orderImportDTO.getPhoneNumber();
                            String countryCode = orderImportDTO.getCountryCode();
                            String stateCode = orderImportDTO.getStateCode();
                            String city = orderImportDTO.getCity();
                            String address1 = orderImportDTO.getAddress1();
                            String address2 = orderImportDTO.getAddress2();
                            String address3 = orderImportDTO.getAddress3();
                            String companyName = orderImportDTO.getCompanyName();
                            String zipCode = orderImportDTO.getZipCode();
                            String logisticsType = orderImportDTO.getLogisticsType();
                            String logisticsCarrier = orderImportDTO.getLogisticsCarrier();
                            String logisticsService = orderImportDTO.getLogisticsService();
                            String logisticsTrackingNo = orderImportDTO.getLogisticsTrackingNo();
                            String channelType = orderImportDTO.getChannelType();



                            SiteCountryCurrency currency = null;
                            try {
                                currency = siteMap.get(countryCode);
                            }catch (Exception e){
                                log.error("获取站点信息失败",e);
                                throw new Exception("获取站点信息失败");
                            }



                            Long siteId = currency.getId();
                            String currencyCode = currency.getCurrencyCode();
                            String currencySymbol = currency.getCurrencySymbol();


//                            ProductSkuDetail skuDetail = iProductSkuDetailService.queryByProductSkuCode(productSkuCode);
                            ProductSkuPrice skuPrice = TenantHelper.ignore(()->iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId));
                            if(ObjectUtil.isEmpty(skuPrice)){
                                throw new Exception("SKU"+productSkuCode+"在此地区未维护价格");
//                                throw new Exception("SKU"+productSkuCode+",站点"+currency.getCountryCode()+"价格不存在");
                            }
                            Warehouse warehouse = null;
                            if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
                                LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode)
                                                                                                           .eq(Warehouse::getDelFlag, 0);
                                warehouse = TenantHelper.ignore(()->iWarehouseService.getOne(wrapper));

                            }
                            // 会员价格
                            ProductSku sku = TenantHelper.ignore(()->iProductSkuService.getById(skuPrice.getProductSkuId())) ;
                            Product product = TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
                            // 是否第三方物流支持
                            Boolean thirdCarrierSupport = false;
                            LogisticsTypeEnum logisticsTypeEnum;
                            Map<String, DeliveryFeeByErpResponse> deliveryFeeByErpMap;
                            List<DeliveryFeeByErpResponse> deliveryFeeByErp = new ArrayList<>();
                            if (StrUtil.equals(logisticsType, "Pick Up")) {
                                logisticsTypeEnum = LogisticsTypeEnum.PickUp;
//                                if (StrUtil.equals(thirdBilling, "Yes")) {  //勾选了第三方物流则需要仓库支持
//                                    thirdCarrierSupport = true;
//                                }
                            } else {
                                logisticsTypeEnum = LogisticsTypeEnum.DropShipping;
                            }
                            BigDecimal originalFinalDeliveryFeeAfterDiscount = null;
                            try{
                                DeliveryFeeByErpRequest deliveryFeeReq = new DeliveryFeeByErpRequest();
                                deliveryFeeReq.setChannelFlag(channelFlag);
                                List<String> stashList = new ArrayList<>();
                                if(ObjectUtil.isNotEmpty(warehouse)){
                                    stashList.add(warehouse.getWarehouseCode());
                                }else {
                                    // todo 改造 增加站点-匹配仓库逻辑
                                    stashList = getStashList(productSkuCode,quantity,countryCode);
                                }

                                // 没有仓库
                                if(CollUtil.isEmpty(stashList)){
                                    log.info("商品缺少库存");
                                }
                                deliveryFeeReq.setOrgWarehouseCodeList(stashList);
                                deliveryFeeReq.setPostcode(zipCode);

                                DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
                                productItem.setErpSku(sku.getSku());
                                productItem.setQuantity(quantity);
                                deliveryFeeReq.setSupplierTenantId(sku.getTenantId());
                                deliveryFeeReq.setDistributorTenantId(dtenantId);
                                deliveryFeeReq.setSkuList(Collections.singletonList(productItem));
                                // 如果是任意就都给
                                if ("任意".equalsIgnoreCase(logisticsCarrier)) {
                                    if (ChannelTypeEnum.Walmart.name().equalsIgnoreCase(channelType)) {
                                        deliveryFeeReq.setCarrierCodes(walmartCarriers);
                                        logisticsCarrier = walmartCarriers.get(0);
                                    } else {
                                        deliveryFeeReq.setCarrierCodes(notWalmartCarriers);
                                        logisticsCarrier = notWalmartCarriers.get(0);
                                    }

                                } else {
                                    deliveryFeeReq.setCarrierCodes(Collections.singletonList(logisticsCarrier));
                                }
                                Boolean approvedTenant = sysTenantService.getIsApprovedTenant(dtenantId, 1);
                                if (approvedTenant && (!StrUtil.equals(logisticsType, "Pick Up"))) {
                                    try {
                                        deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeReq), product.getTenantId());
                                        DeliveryFeeByErpResponse deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
                                        ProductPriceResponse productPrice = priceSupportV2.getProductPrice(sku.getProductSkuCode(), deliveryFeeByErpResponse.getShippingFee(), dtenantId, siteId);


                                        LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseCode, deliveryFeeByErpResponse.getOrgWarehouseCode())
                                                                                                                   .eq(Warehouse::getTenantId, sku.getTenantId());
                                        Warehouse warehouse1 = TenantHelper.ignore(()->iWarehouseService.getOne(wrapper));
                                        originalFinalDeliveryFeeAfterDiscount = productPrice.getDeliveryFee();
                                        warehouseSystemCode = warehouse1.getWarehouseSystemCode();
                                    }catch (Exception e){
                                        throw new Exception("尾程测算失败");
                                    }

                                }else{
                                    // 走会员价
                                    ProductPriceResponse productPrice = priceSupportV2.getProductPrice(productSkuCode, null, dtenantId, siteId);
                                    originalFinalDeliveryFeeAfterDiscount = productPrice.getDeliveryFee();
                                }
                            }catch (Exception e){
                                deliveryFeeByErp = null;
                                originalFinalDeliveryFeeAfterDiscount = null;
                                warehouseSystemCode = null;
                                log.error("获取物流费用失败",e);

                            }

                            BigDecimal platformPickUpPrice;
                            BigDecimal platformDropShippingPrice = null;
                            if (StrUtil.isNotBlank(activityCode)) {  // 活动订单，需要查询活动价格
                                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCode(activityCode);
                                // 因为分销商参加活动时已经支付了定金，所以此处不能直接取自提价/代发价，只能取尾款单价
                                platformPickUpPrice = activityPriceItem.getPlatformBalanceUnitPrice();
                                // 代发价等于尾款单价加上尾程派送费
                                platformDropShippingPrice = NumberUtil.add(platformPickUpPrice, activityPriceItem.getPlatformFinalDeliveryFee());
                            } else {  // 普通订单，直接查询SKU原价
                                // todo 从地址信息内获取国家信息
                                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), LoginHelper.getTenantId(), sku.getId(),siteId );
                                platformPickUpPrice = skuPrice.getPlatformPickUpPrice();
                                if(ObjectUtil.isNotEmpty(originalFinalDeliveryFeeAfterDiscount)){
                                    platformDropShippingPrice = platformPickUpPrice.add(originalFinalDeliveryFeeAfterDiscount);
                                }else {
                                    platformDropShippingPrice = null;
                                }

//                                platformDropShippingPrice = skuPrice.getPlatformDropShippingPrice();
                                if(ObjectUtil.isNotEmpty(memberPrice)){
                                    BigDecimal upPrice = memberPrice.getPlatformPickUpPrice();
//                                    BigDecimal dropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                                    if (ObjectUtil.isNotEmpty(upPrice)){
                                        platformPickUpPrice = upPrice;
                                        if(ObjectUtil.isNotEmpty(originalFinalDeliveryFeeAfterDiscount)){
                                            platformDropShippingPrice = upPrice.add(originalFinalDeliveryFeeAfterDiscount);
                                        }else{
                                            platformDropShippingPrice = null;
                                        }

                                    }

                                }

                            }
                            String finalOrderNo = null;
                            String orderExtendId = null;
                            if("LTL".equals(logisticsCarrier)){
                                orderExtendId = baseOrderExtendIdMap.get(channelOrderNo);
                                boolean isDuplicate = duplicateLtlMap.getOrDefault(channelOrderNo, false);

                                // 生成最终订单号
                                finalOrderNo = isDuplicate ?
                                    orderExtendId + "-" + getSequence(channelOrderNo, sequenceMap) : orderExtendId;

                            }
                            //开始生成临时订单
                            OrderImportTemp temp = new OrderImportTemp();
                            temp.setRecordId(recordId);
                            temp.setChannelType(channelType);
                            temp.setStoreName(channelAlias);
                            temp.setStoreOrderId(channelOrderNo);
                            temp.setProductSkuCode(productSkuCode);
                            temp.setProductQuantity(quantity);
                            temp.setRecipientName(recipientName);
                            temp.setAddress1(address1);
                            temp.setAddress2(address2);
                            temp.setAddress3(address3);
                            temp.setCompanyName(companyName);
                            temp.setCity(city);
                            temp.setStateCode(stateCode);
                            temp.setZipCode(zipCode);
                            temp.setPhoneNumber(phoneNumber);
                            temp.setCountryCode(countryCode);
                            temp.setLogisticsType(logisticsTypeEnum);
                            temp.setSiteId(siteId);
                            temp.setCurrencyCode(currencyCode);
                            temp.setCurrencySymbol(currencySymbol);

                            temp.setWarehouseSystemCode(warehouseSystemCode);
                            // 存原始导入进来的承运商
                            if(StrUtil.equalsIgnoreCase(logisticsType, "DropShipping")){
                                // 存erp返回的测算后的承运商编号
                                if (ObjectUtil.isNotEmpty(deliveryFeeByErp)) {
                                    DeliveryFeeByErpResponse deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
                                    String carrierCode = deliveryFeeByErpResponse.getCarrierCode();
                                    String logisticCode = deliveryFeeByErpResponse.getLogisticCode();
                                    temp.setLogisticsCarrierCode(carrierCode);
                                    temp.setLogisticsCarrier(carrierCode);
                                    temp.setLogisticsServiceName(logisticCode);
                                }else {
                                    temp.setLogisticsCarrierCode(null);
                                    temp.setLogisticsCarrier(null);
                                    temp.setLogisticsServiceName(null);
                                }
                            }else if(StrUtil.equalsIgnoreCase(logisticsType, "Pick Up")) {
                                temp.setLogisticsCarrierCode(logisticsCarrier);
                                temp.setLogisticsCarrier(logisticsCarrier);
                                temp.setLogisticsServiceName(logisticsService);
                            }

//                          LTL订单tracking可为空
                            if (StrUtil.isNotBlank(logisticsTrackingNo)) {
                                temp.setLogisticsTrackingNo(JSONUtil.parseArray(StrUtil.split(logisticsTrackingNo, ";")));
                            }

                            temp.setLogisticsThirdBilling(thirdCarrierSupport);
//                            temp.setLogisticsAccount(carrierAccount);
//                            temp.setLogisticsAccountZipCode(carrierAccountZipCode);
                            // 订单号逻辑
                            if(StrUtil.isNotEmpty(finalOrderNo)&&StrUtil.isNotEmpty(orderExtendId)){
                                temp.setOrderNo(finalOrderNo);
                                temp.setOrderExtendId(orderExtendId);
                            }else {
                                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
                                temp.setOrderNo(orderNo);
                                temp.setOrderExtendId(orderNo);
                            }


//                            temp.setOrderNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo));
                            temp.setTempOrderNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.TempOrderNo));
                            temp.setPickUpPrice(platformPickUpPrice);
                            temp.setPickUpTotalAmount(NumberUtil.mul(platformPickUpPrice, quantity));
                            temp.setDropShippingPrice(platformDropShippingPrice);
                            temp.setDropShippingTotalAmount(ObjectUtil.isEmpty(platformDropShippingPrice)?null:NumberUtil.mul(platformDropShippingPrice, quantity));

                            temp.setShippingFee(originalFinalDeliveryFeeAfterDiscount);
                            temp.setShippingTotalAmount(originalFinalDeliveryFeeAfterDiscount==null?null:NumberUtil.mul(originalFinalDeliveryFeeAfterDiscount, quantity));

                            temp.setTempState(OrderTempState.Temporary);

                            if (StrUtil.isNotBlank(activityCode)) {
                                temp.setActivityCode(activityCode);
                            }

                            //商品信息
//                            Product product = iProductService.queryByProductSkuCode(productSkuCode);
                            String productName = product.getName();
                            ProductSkuAttachmentVo attachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuCode(productSkuCode);
                            temp.setProductName(productName);
                            temp.setProductImageShowUrl(attachmentVo.getAttachmentShowUrl());
                            temp.setOrderSource(OrderSourceEnum.EXCEL_ORDER.getValue());
                            DataUtil.convertNullString2Null(temp);
                            tempList.add(temp);
                        }

                        if (CollUtil.isNotEmpty(tempList)) {
                            record.setImportState(ImportStateEnum.Pending);
                            iOrderImportRecordService.updateById(record);
                            iOrderImportTempService.saveBatch(tempList);
                            vo.setImportState(ImportStateEnum.Pending.name());
                            vo.setTempOrderNoList(tempList.stream().map(OrderImportTemp::getTempOrderNo).collect(Collectors.toList()));
                            return R.ok(vo);
                        } else {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_NOT_EXIST_VALID_ROW);
                        }
                    }
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_NOT_EXIST_VALID_ROW);
                }
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_NOT_EXIST_VALID_ROW);
            }
        } catch (RStatusCodeException e) {
            throw e;
        } catch (ExcelMessageException e) {
            log.error("Excel状态码异常 {}", e.getMessage());
            throw new RStatusCodeException(e.getLocaleMessage().toMessage());
        } catch (RuntimeException e) {
            log.error("上传订单Excel出现未知运行时错误 {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("上传订单Excel出现未知错误 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.UPLOAD_EXCEL_ERROR);
        }
    }
    private int getSequence(String channelOrderNo,Map<String, AtomicInteger> sequenceMap) {
        return sequenceMap.computeIfAbsent(channelOrderNo, k -> new AtomicInteger(1))
                          .getAndIncrement();
    }
    /**
     * 临时订单转正式订单,需要加个幂等锁 ,单个订单转换间隔3秒
     *
     * @param bo
     * @return
     * @throws Exception
     */
    @Override
    @TempOrderLimit(interval = 3, timeUnit = TimeUnit.SECONDS)
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<TempOrder2OrdersVo> tempOrder2Orders(TempOrder2OrdersBo bo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);

        String recordNo = bo.getRecordNo();
        List<String> tempOrderNoList = bo.getTempOrderNoList();
        List<String> orderNoList = new ArrayList<>();

        BigDecimal allProductPrice = BigDecimal.ZERO;
        BigDecimal allShippingFee = BigDecimal.ZERO;
        BigDecimal allTotalPrice = BigDecimal.ZERO;
        List<String> productSkuCodeList = new ArrayList<>();
        if (CollUtil.isEmpty(tempOrderNoList) || StrUtil.isBlank(recordNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        OrderImportRecord orderImportRecord = iOrderImportRecordService.queryByRecordNo(recordNo);
        ImportTypeEnum importType = orderImportRecord.getImportType();

        List<OrderImportTemp> tempOrderList = iOrderImportTempService.queryPendingByTempOrderNoList(tempOrderNoList);
        if (CollUtil.isEmpty(tempOrderList)) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }
        Map<String, SiteCountryCurrency> siteMap = iSiteCountryCurrencyService.getSiteMap();
        for (var tempOrder : tempOrderList) {
            String orderNo = tempOrder.getOrderNo();
            String storeName = tempOrder.getStoreName();
            String storeOrderId = tempOrder.getStoreOrderId();
            String productSkuCode = tempOrder.getProductSkuCode();
            Integer productQuantity = tempOrder.getProductQuantity();
            String recipientName = tempOrder.getRecipientName();
            String address1 = tempOrder.getAddress1();
            String address2 = tempOrder.getAddress2();
            String city = tempOrder.getCity();
            String stateCode = tempOrder.getStateCode();
            String zipCode = tempOrder.getZipCode();
            String phoneNumber = tempOrder.getPhoneNumber();
            String countryCode = tempOrder.getCountryCode();
            LogisticsTypeEnum logisticsType = tempOrder.getLogisticsType();
            String warehouseSystemCode = tempOrder.getWarehouseSystemCode();
            String logisticsCarrier = tempOrder.getLogisticsCarrier();
            String logisticsServiceName = tempOrder.getLogisticsServiceName();
            JSONArray logisticsTrackingNo = tempOrder.getLogisticsTrackingNo();
            Boolean logisticsThirdBilling = tempOrder.getLogisticsThirdBilling() == null ? false : tempOrder.getLogisticsThirdBilling();
            tempOrder.setLogisticsThirdBilling(logisticsThirdBilling);
            String channelTypeTemp = tempOrder.getChannelType();
            Integer orderSource = tempOrder.getOrderSource();
            BigDecimal dropShippingPrice = tempOrder.getDropShippingPrice();
            String logisticsAccount = tempOrder.getLogisticsAccount();
            String logisticsAccountZipCode = tempOrder.getLogisticsAccountZipCode();
            BigDecimal shippingFee = tempOrder.getShippingFee();
            Long shippingLabelOssId = tempOrder.getShippingLabelOssId();
            String shippingLabelFileName = tempOrder.getShippingLabelFileName();
            String activityCode = tempOrder.getActivityCode();
            SiteCountryCurrency currency = siteMap.get(countryCode);

            Long siteId = currency.getId();
            String countryName = currency.getCountryName();
            String currencyCode = currency.getCurrencyCode();
            String currencySymbol = currency.getCurrencySymbol();


            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                if (!logisticsThirdBilling && shippingLabelOssId == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                }

                if (!logisticsThirdBilling && CollUtil.isEmpty(logisticsTrackingNo)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_TRACKING_NO_BLANK_ERROR);
                }

                if (logisticsThirdBilling && StrUtil.isBlank(logisticsAccount)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR);
                }

                if (logisticsThirdBilling && StrUtil.isBlank(logisticsAccountZipCode)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR);
                }

                if (StrUtil.isBlank(logisticsCarrier)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SHIPPING_NAME_CAN_NOT_NULL);
                }
                if (StrUtil.isBlank(warehouseSystemCode)){
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_EXIST_ERROR);
                }
            }
            ChannelTypeEnum channelType;
            // 如果未指定渠道 走原逻辑
            if(StrUtil.isNotEmpty(channelTypeTemp)){
                // 拿字典表
                channelType = ChannelTypeEnum.getChannelTypeEnum(channelTypeTemp);
            }else{
                channelType = ObjUtil.equals(importType, ImportTypeEnum.ShippingCart) ?
                    ChannelTypeEnum.Marketplace : ChannelTypeEnum.Others;
            }
            // 规定渠道

            HashMap<String, Integer> codesMap = new HashMap<String, Integer>();
            // 开始生成订单
            Orders order = new Orders();
            order.setOrderNo(orderNo);
            order.setChannelAlias(storeName);
            order.setChannelOrderNo(storeOrderId);
            order.setChannelOrderName(storeOrderId);
            order.setChannelType(channelType);
            order.setTotalQuantity(productQuantity);
            order.setOrderType(OrderType.Normal);
            order.setLogisticsType(logisticsType);
            order.setOrderState(OrderStateType.UnPaid);
            order.setFulfillmentProgress(LogisticsProgress.UnDispatched );
            order.setOrderExtendId(orderNo);
            order.setOrderSource(orderSource);

            order.setSiteId(siteId);
            order.setCountryCode(countryCode);
            order.setCurrency(currencyCode);
            order.setCurrencySymbol(currencySymbol);

            iOrdersService.save(order);

            Long orderId = order.getId();

            OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();

            orderLogisticsInfo.setOrderId(orderId);
            orderLogisticsInfo.setOrderNo(orderNo);
            orderLogisticsInfo.setLogisticsType(logisticsType);
            orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
            orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);
            orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrier);
            orderLogisticsInfo.setLogisticsServiceName(logisticsServiceName);
            orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
            orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);
            if (shippingLabelOssId != null) {
                orderLogisticsInfo.setShippingLabelExist(true);
                orderLogisticsInfo.setShippingLabelOssId(shippingLabelOssId);
                orderLogisticsInfo.setShippingLabelFileName(shippingLabelFileName);
            }

            orderLogisticsInfo.setZipCode(StrUtil.trim(zipCode));
            orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zipCode));
            if (StrUtil.contains(zipCode, "-")) {
                // 存在-的邮编，需要分割出前面5位的主邮编
                String mainZipCode = StrUtil.trim(StrUtil.split(zipCode, "-").get(0));
                orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
            }
            orderLogisticsInfo.setLogisticsCountryCode(countryCode);

            orderAddressInfo.setOrderId(orderId);
            orderAddressInfo.setOrderNo(orderNo);
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            orderAddressInfo.setRecipient(recipientName);
            orderAddressInfo.setPhoneNumber(phoneNumber);
            orderAddressInfo.setCountry(countryCode);
            orderAddressInfo.setCountryCode(countryCode);
            orderAddressInfo.setState(stateCode);
            orderAddressInfo.setStateCode(stateCode);
            orderAddressInfo.setCity(city);
            orderAddressInfo.setAddress1(address1);
            orderAddressInfo.setAddress2(address2);
            orderAddressInfo.setZipCode(zipCode);

            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseSystemCode);
            if (productSku == null) {
                if (StrUtil.isNotBlank(warehouseSystemCode)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE.args(productSkuCode, warehouseSystemCode));
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_SOME_PRODUCT_NOT_EXIST.args(productSkuCode));
                }
            }

            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            paramDTO.setActivityCode(activityCode);
            paramDTO.setCountry(countryCode);
            paramDTO.setChannelTypeEnum(channelType);
            paramDTO.setLogisticsType(logisticsType);
            paramDTO.setLogisticsThirdBilling(logisticsThirdBilling);
            paramDTO.setWarehouseSystemCode(warehouseSystemCode);
            OrderItemDTO orderItemDTO = orderSupport.generateOrderItem(LoginHelper.getTenantId(), order, productSku, productQuantity, paramDTO, zipCode, logisticsCarrier);
            String logisticsCarrierCode = paramDTO.getLogisticsCarrierCode();
            if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrierCode);
                orderLogisticsInfo.setLogisticsCarrierCode(logisticsCarrierCode);
                orderLogisticsInfo.setLogisticsServiceName(paramDTO.getLogisticsCode());
            }
            OrderItem orderItem = orderItemDTO.getOrderItem();
            OrderItemPrice orderItemPrice = orderItemDTO.getOrderItemPrice();
            OrderItemProductSku orderItemProductSku = orderItemDTO.getOrderItemProductSku();
            LocaleMessage localeMessage = orderItemDTO.getLocaleMessage();

            if (localeMessage.hasData()) {
                orderLogisticsInfo.setLogisticsErrorMessage(localeMessage.toJSON());
            }

            // 处理物流信息
            List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
            if (LogisticsTypeEnum.PickUp.equals(logisticsType) && !logisticsThirdBilling) {
                for (int i = 0; i < logisticsTrackingNo.size(); i++) {
                    String trackingNo = logisticsTrackingNo.getStr(i);
                    String trimTracking = StrUtil.trim(trackingNo);
                    if (StrUtil.isNotBlank(trimTracking)) {
                        OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
                        trackingRecord.setSku(productSku.getSku());
                        trackingRecord.setProductSkuCode(productSkuCode);
                        if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                            trackingRecord.setLogisticsCarrier(logisticsCarrierCode);
                        }else {
                            trackingRecord.setLogisticsCarrier(logisticsCarrier);
                        }

                        trackingRecord.setLogisticsService(logisticsServiceName);
                        trackingRecord.setLogisticsTrackingNo(trimTracking);
                        trackingRecord.setOrderNo(orderNo);
                        trackingRecord.setOrderItemNo(orderItem.getOrderItemNo());
                        trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
                        trackingRecord.setWarehouseSystemCode(warehouseSystemCode);

                        if (CollUtil.size(logisticsTrackingNo) == productQuantity) {
                            trackingRecord.setQuantity(1);
                        } else {
                            trackingRecord.setQuantity(productQuantity);
                        }
                        trackingList.add(trackingRecord);
                    }
                }
            }

            BigDecimal platformPickUpPrice;
            BigDecimal platformDropShippingPrice = null;
            if (StrUtil.isNotBlank(activityCode)) {  // 活动订单，需要查询活动价格
                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCode(activityCode);
                // 因为分销商参加活动时已经支付了定金，所以此处不能直接取自提价/代发价，只能取尾款单价
                platformPickUpPrice = activityPriceItem.getPlatformBalanceUnitPrice();
                // 代发价等于尾款单价加上尾程派送费
                platformDropShippingPrice = NumberUtil.add(platformPickUpPrice, activityPriceItem.getPlatformFinalDeliveryFee());
            } else {  // 普通订单，直接查询SKU原价
                ProductSkuPrice skuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId);

                ProductSku sku = TenantHelper.ignore(()->iProductSkuService.getById(skuPrice.getProductSkuId())) ;
                Product product = TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), LoginHelper.getTenantId(), sku.getId(), siteId);
                platformPickUpPrice = skuPrice.getPlatformPickUpPrice();
                platformDropShippingPrice = dropShippingPrice;
//                platformDropShippingPrice = skuPrice.getPlatformDropShippingPrice();
                if(ObjectUtil.isNotEmpty(memberPrice)){
                    if(ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())){
                        platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                        if(ObjectUtil.isNotEmpty(shippingFee)){
                            platformDropShippingPrice = platformPickUpPrice.add(shippingFee);
                        }

                    }
                }

            }

            priceSupportV2.recalculateOrderAmount(order, CollUtil.newArrayList(orderItemPrice));
            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                codesMap.put(order.getOrderNo(),order.getExceptionCode());
            }
            // todo 此处要按照setNull进行判断
//            iOrdersService.updateById(order);
            iOrdersService.updateBatchOrSetNull(Collections.singletonList(order),codesMap);
            iOrderLogisticsInfoService.save(orderLogisticsInfo);
            iOrderAddressInfoService.save(orderAddressInfo);

            orderItem.setOrderId(orderId);

            orderItem.setSiteId(siteId);
            orderItem.setCountryCode(countryCode);
            orderItem.setCurrency(currencyCode);
            orderItem.setCurrencySymbol(currencySymbol);

            iOrderItemService.save(orderItem);
            Long orderItemId = orderItem.getId();

            orderItemPrice.setSiteId(siteId);
            orderItemPrice.setCountryCode(countryCode);
            orderItemPrice.setCurrency(currencyCode);
            orderItemPrice.setCurrencySymbol(currencySymbol);

            orderItemPrice.setOrderItemId(orderItemId);
            iOrderItemPriceService.save(orderItemPrice);

            orderItemProductSku.setOrderItemId(orderItemId);
            iOrderItemProductSkuService.save(orderItemProductSku);

            iOrderItemTrackingRecordService.saveBatch(trackingList);

            tempOrder.setPickUpTotalAmount(NumberUtil.mul(platformPickUpPrice, productQuantity));
            if(ObjectUtil.isNotEmpty(platformDropShippingPrice)){
                tempOrder.setDropShippingTotalAmount(NumberUtil.mul(platformDropShippingPrice, productQuantity));
            }else {
                tempOrder.setDropShippingTotalAmount(null);
            }

            tempOrder.setTempState(OrderTempState.Official);

            orderNoList.add(orderNo);
            BigDecimal platformActualTotalAmount = order.getPlatformActualTotalAmount();
            if(ObjectUtil.isNotEmpty(platformActualTotalAmount)){
                allProductPrice = allProductPrice.add(platformActualTotalAmount);
                allTotalPrice = allTotalPrice.add(order.getPlatformActualTotalAmount());
            }

            productSkuCodeList.add(productSkuCode);
            if (ObjUtil.equals(importType, ImportTypeEnum.ShippingCart)) {
                if (ObjectUtil.equal(logisticsType.name(),LogisticsTypeEnum.PickUp.name())) {
                    iUserShippingCartService.deleteByProductSkuCode(productSkuCode,SupportedLogisticsEnum.PickUpOnly.name());
                }
                if (ObjectUtil.equal(logisticsType.name(),LogisticsTypeEnum.DropShipping.name())) {
                    iUserShippingCartService.deleteByProductSkuCode(productSkuCode,SupportedLogisticsEnum.DropShippingOnly.name());
                }
            }
        }

        iOrderImportTempService.updateBatchById(tempOrderList);

        orderImportRecord.setImportState(ImportStateEnum.Success);
        orderImportRecord.setImportOrders(CollUtil.size(tempOrderList));
        iOrderImportRecordService.updateById(orderImportRecord);
        TempOrder2OrdersVo vo = new TempOrder2OrdersVo();
        vo.setOrderNoList(orderNoList);
        vo.setAllProductPrice(allProductPrice);
        vo.setAllShippingFee(allShippingFee);
        vo.setAllTotalPrice(allProductPrice);
        return R.ok(vo);
    }

    @Override
    public R<TempOrder2OrdersVo> tempOrder2OrdersV2(TempOrder2OrdersBo bo) throws Exception {
        String recordNo = bo.getRecordNo();
        updateOption(recordNo, 1);
        R<TempOrder2OrdersVo> r ;
        // 拆到业务实现里
        try{
            r = iOrderImportRecordManger.tempOrder2Orders(bo);
        } catch (Exception e) {
            updateOption(recordNo, 3);
            log.error("导单失败",e);
            throw e;
        }
        updateOption(recordNo, 2);
        return r;
    }

    private void updateOption(String recordNo, int i) {
        iOrderImportRecordService.updateForNewTransact(recordNo,i);
    }

    /**
     * 功能描述：压平oss-ids
     *
     * @param tempList 临时清单
     * @return {@link List }<{@link Long }>
     * <AUTHOR>
     * @date 2025/05/21
     */
    public List<Long> flattenOssIds(List<OrderImportTemp> tempList) {
        return tempList.stream()
                       // 平铺 bolOssId 和其他 JSON 中的 ids
                       .flatMap(temp -> {
                           // 1. 提取 bolOssId
                           List<Long> combinedIds = new ArrayList<>();
                           if (temp.getBolOssId() != null) {
                               combinedIds.add(temp.getBolOssId());
                           }

                           // 2. 提取其他 JSON 中的 ids（例如 otherOssIds）
                           if (temp.getOtherOssIds() != null && temp.getOtherOssIds().get("ids") != null) {
                               combinedIds.addAll((Collection<? extends Long>) temp.getOtherOssIds().get("ids"));
                           }

                           // 类似处理 cartonlabelOssIds、itemlabelOssIds 等字段
                           if (temp.getCartonLabelOssIds() != null && temp.getCartonLabelOssIds().get("ids") != null) {
                               combinedIds.addAll((Collection<? extends Long>) temp.getCartonLabelOssIds().get("ids"));
                           }

                           if (temp.getPalletLabelOssIds() != null && temp.getPalletLabelOssIds().get("ids") != null) {
                               combinedIds.addAll((Collection<? extends Long>) temp.getPalletLabelOssIds().get("ids"));
                           }

                           if (temp.getItemLabelOssIds() != null && temp.getItemLabelOssIds().get("ids") != null) {
                               combinedIds.addAll((Collection<? extends Long>) temp.getItemLabelOssIds().get("ids"));
                           }

                           return combinedIds.stream();
                       })
                       .collect(Collectors.toList());
    }

    /**
     * 查询导入记录详情
     *
     * @param bo
     * @return
     */
    @Override
    public R<OrderImportRecordDetailVo> queryImportRecordDetail(OrderImportRecordBo bo) {
        String recordNo = bo.getRecordNo();
        OrderImportRecord orderImportRecord = iOrderImportRecordService.queryByRecordNo(recordNo);
        if (orderImportRecord == null) {
            return R.fail(ZSMallStatusCodeEnum.IMPORT_RECORD_NOT_FOUND);
        }

        Long recordId = orderImportRecord.getId();
        Integer orderTempState = orderImportRecord.getOrderTempState();
        Date createTime = orderImportRecord.getCreateTime();
        ImportStateEnum importState = orderImportRecord.getImportState();
        String importFileName = orderImportRecord.getImportFileName();
        JSONObject importMessage = orderImportRecord.getImportMessage();
        // 导入订单页面只支持一个币种
        String currencySymbol = null;

        OrderImportRecordDetailVo vo = new OrderImportRecordDetailVo();
        vo.setRecordNo(recordNo);
        vo.setCreateDateTime(DateUtil.formatDateTime(createTime));
        vo.setImportFileName(importFileName);
        vo.setImportMessage(importMessage);
        vo.setImportState(importState.getValue());

        OrderTempState tempState = OrderTempState.Temporary;
        if (ImportStateEnum.Success.equals(importState)) {
            tempState = OrderTempState.Official;
        }

        List<OrderImportTemp> tempList = iOrderImportTempService.queryByRecordIdAndTempState(recordId, tempState);
        Map<String, OrderImportTemp> importTempMap = tempList.stream()
                                                       .collect(Collectors.toMap(OrderImportTemp::getTempOrderNo, Function.identity()));
        List<Long> ids = flattenOssIds(tempList);
        List<SysOssVo> sysOssVos = null;
        Map<Long, SysOssVo> sysOssVoMap;
        if(CollUtil.isNotEmpty(ids)){
            sysOssVos = iSysOssService.listByOssIds(ids);
            sysOssVoMap = sysOssVos.stream()
                                   .collect(Collectors.toMap(SysOssVo::getOssId, Function.identity()));
        } else {
            sysOssVoMap = null;
        }

        // 拿出tempList内的附件id,bolOssId属性是id,otherOssId是json,key=ids,value是jsonArray,全部放入list

        List<TempOrderVo> tempVoList = BeanUtil.copyToList(tempList, TempOrderVo.class);
        String ruleCustomizerTenantId = null;
        List<Long> productSkuIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(tempVoList)) {
            List<String> productSkuCodeList = new ArrayList<>();
            for (TempOrderVo tempOrder : tempVoList) {
                if(StringUtils.isNotEmpty(tempOrder.getProductSkuCode())){
                    productSkuCodeList.add(tempOrder.getProductSkuCode());
                }
            }
            if(CollectionUtil.isNotEmpty(productSkuCodeList)){
                List<ProductSku> productSkus = iProductSkuService.queryByProductSkuCodeList(productSkuCodeList);
                if(CollectionUtil.isNotEmpty(productSkus)){
                    for (ProductSku productSku : productSkus){
                        if(StringUtils.isEmpty(ruleCustomizerTenantId)){
                            ruleCustomizerTenantId = productSku.getTenantId();
                        }
                        productSkuIdList.add(productSku.getId());
                    }
                    loop: for (TempOrderVo tempOrder : tempVoList) {
                        for (ProductSku productSku : productSkus){
                            if(tempOrder.getProductSkuCode().equals(productSku.getProductSkuCode())){
                                tempOrder.setProductSkuId(productSku.getId());
                                continue loop;
                            }
                        }
                    }
                }
            }
        }
        BigDecimal productAmount = BigDecimal.ZERO;
        BigDecimal shippingFee = BigDecimal.ZERO;
        //是否展示* 如果有一个询价失败,即 tempOrder.getDropShippingPrice()==null 或者tempOrder.getDropShippingTotalAmount()==null则展示
        // 是否所有的运费都为空
        Boolean deliveryFeeIsNull = true;
        Integer dropShippingNum = 0;
        if (CollUtil.isNotEmpty(tempVoList)) {
            // 活动定金比例
            JSONObject ACTIVITY_DEPOSIT_PRICE_PERCENT = businessParameterService.getValueFromJSONObject(BusinessParameterType.ACTIVITY_DEPOSIT_PRICE_PERCENT);
            for (TempOrderVo tempOrder : tempVoList) {

                String orderNo = tempOrder.getOrderNo();
                String logisticsType = tempOrder.getLogisticsType();
                if (LogisticsTypeEnum.DropShipping.name().equals(logisticsType)) {
                    dropShippingNum++;
                    if(ObjectUtil.isNotEmpty(tempOrder.getDropShippingTotalAmount())){
                        productAmount = NumberUtil.add(productAmount, tempOrder.getDropShippingTotalAmount());
                        shippingFee = NumberUtil.add(shippingFee, tempOrder.getShippingFee());
                        deliveryFeeIsNull = false;

                    }

                } else {
                    productAmount = NumberUtil.add(productAmount, tempOrder.getPickUpTotalAmount());
                }

                //洲和国家id
                String countryCode = tempOrder.getCountryCode();
                SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(countryCode);
                // 导入业务只有一个站点
                currencySymbol = site.getCurrencySymbol();
                tempOrder.setCurrencySymbol(currencySymbol);
                Long countryId = 0L;
                Long stateId = 0L;

                WorldLocation country = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
                if (country != null) {
                    countryId = country.getId();
                }

                if (StrUtil.equals(countryCode, "US")) {
                    String stateCode = tempOrder.getState();
                    WorldLocation state = iWorldLocationService.queryByParentIdAndLocationCode(countryId, stateCode, LocationTypeEnum.State);
                    if (state != null) {
                        stateId = state.getId();
                    }
                }
                tempOrder.setCountryId(countryId);
                tempOrder.setStateId(stateId);

                OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
                if (orderAttachment != null) {
                    Long ossId = orderAttachment.getOssId();
                    String fileName = orderAttachment.getAttachmentOriginalName();
                    String showUrl = orderAttachment.getAttachmentShowUrl();
                    tempOrder.setAttachmentOssId(ossId);
                    tempOrder.setAttachmentFileName(fileName);
                    tempOrder.setAttachmentShowUrl(showUrl);
                }
                OrderImportTemp orderImportTemp = importTempMap.get(tempOrder.getTempOrderNo());
                JSONObject otherOssIdsJson = orderImportTemp.getOtherOssIds();
                Long bolLabelOssId = orderImportTemp.getBolOssId();
                JSONObject cartonlabelOssIdsJson = orderImportTemp.getCartonLabelOssIds();
                JSONObject palletlabelOssIdsJson = orderImportTemp.getPalletLabelOssIds();
                JSONObject itemlabelOssIdsJson = orderImportTemp.getItemLabelOssIds();

                List<Long> cartonlabelOssIds = JSONObjectUtils.extractIds(cartonlabelOssIdsJson);
                List<Long> palletlabelOssIds = JSONObjectUtils.extractIds(palletlabelOssIdsJson);
                List<Long> itemlabelOssIds = JSONObjectUtils.extractIds(itemlabelOssIdsJson);
                List<Long> otherOssIds = JSONObjectUtils.extractIds(otherOssIdsJson);
                Optional.ofNullable(sysOssVoMap)
                        .filter(CollUtil::isNotEmpty)
                        .map(map -> map.get(bolLabelOssId))
                        .filter(ObjectUtil::isNotEmpty)
                        .ifPresent(sysOssVo -> {
                            tempOrder.setBolLabelFileName(sysOssVo.getFileName());
                            tempOrder.setBolLabelOssId(sysOssVo.getOssId());
                            tempOrder.setBolLabelShowUrl(sysOssVo.getUrl());
                        }
                );

                HashMap<String, List<LabelVo>> labelVoHashMap = new HashMap<>();
                Optional.ofNullable(cartonlabelOssIds)
                        .orElse(Collections.emptyList())
                        .stream()
                        .forEach(id ->
                            Optional.ofNullable(sysOssVoMap)
                                    .filter(CollUtil::isNotEmpty)
                                    .map(map -> map.get(id))  // 使用单个 id 作为键
                                    .filter(ObjectUtil::isNotEmpty)
                                    .ifPresent(sysOssVo -> {
                                        // 根据业务需求设置字段，例如：
                                        LabelVo labelVo = new LabelVo();
                                        labelVo.setLabelOssId(sysOssVo.getOssId());
                                        labelVo.setLabelFileName(sysOssVo.getFileName());
                                        labelVo.setLabelShowUrl(sysOssVo.getUrl());
                                        // 如果 labelVoHashMap 内有key"cartonLabel",则将元素放入value List<LabelVo> 内,如果没有则创建
                                        labelVoHashMap
                                            .computeIfAbsent("cartonLabel", k -> new ArrayList<>())
                                            .add(labelVo);
                                    }
                        )
                );
                Optional.ofNullable(palletlabelOssIds)
                        .orElse(Collections.emptyList())
                        .stream()
                        .forEach(id ->
                            Optional.ofNullable(sysOssVoMap)
                                    .filter(CollUtil::isNotEmpty)
                                    .map(map -> map.get(id))  // 使用单个 id 作为键
                                    .filter(ObjectUtil::isNotEmpty)
                                    .ifPresent(sysOssVo -> {
                                            // 根据业务需求设置字段，例如：
                                            LabelVo labelVo = new LabelVo();
                                            labelVo.setLabelOssId(sysOssVo.getOssId());
                                            labelVo.setLabelFileName(sysOssVo.getFileName());
                                            labelVo.setLabelShowUrl(sysOssVo.getUrl());
                                            // 如果 labelVoHashMap 内有key"cartonLabel",则将元素放入value List<LabelVo> 内,如果没有则创建
                                            labelVoHashMap
                                                .computeIfAbsent("palletLabel", k -> new ArrayList<>())
                                                .add(labelVo);
                                        }
                                    )
                        );

                Optional.ofNullable(itemlabelOssIds)
                        .orElse(Collections.emptyList())
                        .stream()
                        .forEach(id ->
                            Optional.ofNullable(sysOssVoMap)
                                    .filter(CollUtil::isNotEmpty)
                                    .map(map -> map.get(id))  // 使用单个 id 作为键
                                    .filter(ObjectUtil::isNotEmpty)
                                    .ifPresent(sysOssVo -> {
                                            // 根据业务需求设置字段，例如：
                                            LabelVo labelVo = new LabelVo();
                                            labelVo.setLabelOssId(sysOssVo.getOssId());
                                            labelVo.setLabelFileName(sysOssVo.getFileName());
                                            labelVo.setLabelShowUrl(sysOssVo.getUrl());
                                            // 如果 labelVoHashMap 内有key"cartonLabel",则将元素放入value List<LabelVo> 内,如果没有则创建
                                            labelVoHashMap
                                                .computeIfAbsent("itemLabel", k -> new ArrayList<>())
                                                .add(labelVo);
                                        }
                                    )
                        );

                Optional.ofNullable(otherOssIds)
                        .orElse(Collections.emptyList())
                        .stream()
                        .forEach(id ->
                            Optional.ofNullable(sysOssVoMap)
                                    .filter(CollUtil::isNotEmpty)
                                    .map(map -> map.get(id))  // 使用单个 id 作为键
                                    .filter(ObjectUtil::isNotEmpty)
                                    .ifPresent(sysOssVo -> {
                                            // 根据业务需求设置字段，例如：
                                            LabelVo labelVo = new LabelVo();
                                            labelVo.setLabelOssId(sysOssVo.getOssId());
                                            labelVo.setLabelFileName(sysOssVo.getFileName());
                                            labelVo.setLabelShowUrl(sysOssVo.getUrl());
                                            // 如果 labelVoHashMap 内有key"cartonLabel",则将元素放入value List<LabelVo> 内,如果没有则创建
                                            labelVoHashMap
                                                .computeIfAbsent("otherLabel", k -> new ArrayList<>())
                                                .add(labelVo);
                                        }
                                    )
                        );
                if(CollUtil.isNotEmpty(labelVoHashMap)){
                    tempOrder.setLabelVoMap(labelVoHashMap);
                }
                OrderAttachment shippingLabel = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
                if (shippingLabel != null) {
                    String showUrl = shippingLabel.getAttachmentShowUrl();
                    tempOrder.setShippingLabelShowUrl(showUrl);
                }

                ProductActivityTypeEnum activityTypeEnum = ProductActivityTypeEnum.identifyByActivityCode(tempOrder.getActivityCode());
                tempOrder.setActivityType(EnumUtil.toString(activityTypeEnum));

                if (ObjUtil.isNotNull(activityTypeEnum)) {
                    BigDecimal depositPricePercent = new BigDecimal(ACTIVITY_DEPOSIT_PRICE_PERCENT.getStr(activityTypeEnum.name()));
                    tempOrder.setDepositPricePercent(depositPricePercent.multiply(new BigDecimal(100)));
                }

                List<TempOrderAvailableActivityVo> availableActivities = new ArrayList<>();
                ProductActivityTypeEnum[] values = ProductActivityTypeEnum.values();
                for (ProductActivityTypeEnum typeEnum : values) {
                    List<ActivityItemSimpleVo> simpleVoList = iProductActivityItemService.querySelectByProductSkuCode(tempOrder.getProductSkuCode(), typeEnum, ChannelTypeEnum.Others);
                    if (CollUtil.isNotEmpty(simpleVoList)) {
                        List<TempOrderAvailableActivityInfoVo> children = BeanUtil.copyToList(simpleVoList, TempOrderAvailableActivityInfoVo.class);
                        availableActivities.add(new TempOrderAvailableActivityVo(typeEnum.name(), typeEnum.name(), children));
                    }
                }
                tempOrder.setWarehouseCode(tempOrder.getWarehouseSystemCode());
                tempOrder.setShippingFee(tempOrder.getShippingFee());

                tempOrder.setAvailableActivities(availableActivities);
                //查询地址信息
                if (ObjectUtil.equals(tempOrder.getLogisticsType(),LogisticsTypeEnum.PickUp.name()) && ObjectUtil.isNotNull(tempOrder.getWarehouseSystemCode())){
                    String warehouseAddressInfo =TenantHelper.ignore(()->iWarehouseService.getWarehouseAddressInfo(tempOrder.getWarehouseSystemCode()));
                    tempOrder.setWarehouseInfo(warehouseAddressInfo);
                }
                //设置商品支持的物流方式字段
                Product product = iProductService.queryByProductSkuCode(tempOrder.getProductSkuCode());
                if (ObjectUtil.isNotNull(product)){
                    tempOrder.setProductSupportedLogistics(product.getSupportedLogistics().name());
                }
                //设置商品自提/代发的库存数量
                SkuStock skuStock = TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSkuStock(tempOrder.getProductSkuCode()));
                tempOrder.setPickUpStockTotal(ObjectUtil.isNull(skuStock) ? 0 : skuStock.getPickUpStockTotal());
                tempOrder.setDropShippingStockTotal(ObjectUtil.isNull(skuStock) ? 0 : skuStock.getDropShippingStockTotal());
            }
        }
        vo.setAllProductPrice(String.valueOf(productAmount));
        // 这里应该是算总计 目前没地方用到 todo
        vo.setAllShippingFee(DecimalUtil.bigDecimalToString(shippingFee));
        vo.setAllTotalPrice(DecimalUtil.bigDecimalToString(productAmount.add(shippingFee)));
        vo.setTempOrderList(tempVoList);
        if(ObjectUtil.isNotEmpty(tempVoList)){
            vo.setLogisticsMethod(tempVoList.get(0).getLogisticsType());
        }
        vo.setAllProductPrice(DecimalUtil.bigDecimalToString(productAmount));
        vo.setOrderTempState(String.valueOf(orderTempState));
        vo.setAllShippingFee(DecimalUtil.bigDecimalToString(shippingFee));
        vo.setAllTotalPrice(DecimalUtil.bigDecimalToString(productAmount));
        // 如果所有的费用都为空，则不显示
        if(dropShippingNum==tempList.size()&&deliveryFeeIsNull){
            vo.setAllProductPrice(null);
            vo.setAllShippingFee(null);
            vo.setAllTotalPrice(null);
        }
        vo.setCurrencySymbol(currencySymbol);
        vo.setTempOrderList(tempVoList);
        return R.ok(vo);
    }

    /**
     * 更新临时订单
     * @param bo
     * @return
     * @throws Exception
     */
    @Override
    public R<Void> updateTempOrder(TempOrderUpdateBo bo, String updateType) throws Exception {
        String tempOrderNo = bo.getTempOrderNo();
        Long attachmentOssId = bo.getAttachmentOssId();
        String errorMessage = null;
        String dtenantId = LoginHelper.getTenantId();
        if (StrUtil.isBlank(tempOrderNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        OrderImportTemp tempOrder = iOrderImportTempService.queryByTempOrderNoAndTempState(tempOrderNo, OrderTempState.Temporary);
        String carrier = tempOrder.getLogisticsCarrier();
        JSONObject otherOssIds = tempOrder.getOtherOssIds();
        JSONObject cartonlabelOssIds = tempOrder.getCartonLabelOssIds();
        JSONObject itemlabelOssIds = tempOrder.getItemLabelOssIds();
        JSONObject palletlabelOssIds = tempOrder.getPalletLabelOssIds();


        String productSkuCode = tempOrder.getProductSkuCode();
        Integer productQuantity = tempOrder.getProductQuantity();
        if (tempOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }
        Long tempOrderId = tempOrder.getId();
        String orderNo = tempOrder.getOrderNo();
        String orderExtendId = tempOrder.getOrderExtendId();
        // 查出子订单
        List<OrderImportTemp> tempOrders = iOrderImportTempService.queryByOrderExtendId(orderExtendId);
        Map<String, List<String>> tempOrdersMap = tempOrders.stream()
                                                        .collect(Collectors.groupingBy(
                                                            OrderImportTemp::getOrderExtendId,
                                                            Collectors.mapping(
                                                                OrderImportTemp::getOrderNo,
                                                                Collectors.toList()
                                                            )
                                                        ));
        // 将tempOrders转换为map根据orderExtendId为key,value为OrderImportTemp的orderNo元素集合
        Boolean postCodeChange=false;
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);

        /**
         * 更新类型：
         * AddressInfo-地址信息，
         * LogisticsInfo-物流信息，
         * ChannelInfo-渠道信息，
         * UploadShippingLabel-上传快递标签，
         * DeleteShippingLabel-删除快递标签，
         * UploadOrderAttachment-上传订单附件
         * DeleteOrderAttachment-删除订单附件
         */
        if (StrUtil.equals(updateType, "AddressInfo")) {
            //如果是代发
            if (ObjectUtil.equal(tempOrder.getLogisticsType(),LogisticsTypeEnum.DropShipping)){
                //判断更新邮编不能为空
                if (ObjectUtil.isNull(bo.getAddressInfo().getZipCode())){
                    return  R.fail("地址邮编不能为空");
                }
                //判断邮编是否改变且是代发订单
                if (!ObjectUtil.equal(tempOrder.getZipCode(), bo.getAddressInfo().getZipCode())) {
                    postCodeChange=true;
                }
            }
            TempOrderUpdateBo.AddressInfo addressInfo = bo.getAddressInfo();
            String recipientName = addressInfo.getRecipientName();
            String phoneNumber = addressInfo.getPhoneNumber();
            String address1 = addressInfo.getAddress1();
            String address2 = addressInfo.getAddress2();
            String city = addressInfo.getCity();
            Long countryId = addressInfo.getCountryId();
            Long stateId = addressInfo.getStateId();
            String stateCode = addressInfo.getState();

            String zipCode = addressInfo.getZipCode();

            WorldLocationVo country = iWorldLocationService.queryById(countryId);
            String countryCode = country.getLocationCode();
            Long siteId = null;
            SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(countryCode);
            siteId = site.getId();
            if (stateId != null) {
                WorldLocationVo state = iWorldLocationService.queryById(stateId);
                if (ObjectUtil.isNotNull(state)){
                    stateCode = state.getLocationCode();
                }
            }

            String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
            // 支持的国家
            List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
            // 支持的州
            List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");

            if (StrUtil.isBlank(recipientName)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_RECIPIENT_NAME_BLANK_ERROR);
            }
            if (StrUtil.isBlank(phoneNumber)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_BLANK_ERROR);
            }
            if (StrUtil.isBlank(address1)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ADDRESS1_BLANK_ERROR);
            }
            if (StrUtil.isBlank(city)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CITY_BLANK_ERROR);
            }

            // 校验手机号
            if (StrUtil.isNotBlank(phoneNumber) && StrUtil.length(phoneNumber) > 80) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_ERROR);
            }

            // 校验国家
            if (StrUtil.equals(countryCode, "US")) {
                if (ObjectUtil.equals(countryId, 0L)) {
                    return R.fail(ZSMallStatusCodeEnum.UNSUPPORTED_COUNTRIES);
                }

                if (StrUtil.isBlank(zipCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_BLANK_ERROR);
                }

                // 仅US的才校验州
                if (!SUPPORT_STATE.contains(stateCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STATE_NOT_EXIST);
                }
                // 仅US的才校验邮编
                if (!RegexUtil.matchUSPostalCode(zipCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_NOT_EXIST);
                }
            } else if (!SUPPORT_COUNTRY.contains(countryCode)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_COUNTRY_NOT_EXIST);
            }

            tempOrder.setRecipientName(recipientName);
            tempOrder.setPhoneNumber(phoneNumber);
            tempOrder.setAddress1(address1);
            tempOrder.setAddress2(address2);
            tempOrder.setCity(city);
            tempOrder.setStateCode(stateCode);
            tempOrder.setCountryCode(countryCode);
            tempOrder.setZipCode(zipCode);

            if(approvedTenant){
                if (postCodeChange){
                    // 实际可能为null 实际为测算异常
                    List<String> stashList = null;
                    try {
                        stashList  = getStashList(productSkuCode, productQuantity, countryCode);
                    }catch (Exception e){
                        errorMessage = "库存不足,获取派送费失败";
                        log.error("获取运费失败",e);
                        log.error("获取运费失败",e);
                        tempOrder.setShippingFee(null);
                        tempOrder.setShippingTotalAmount(null);
                        tempOrder.setWarehouseSystemCode(null);
                        tempOrder.setDropShippingPrice(null);
                        tempOrder.setDropShippingTotalAmount(null);
                    }

                    ProductSku productSku = TenantHelper.ignore(()->iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                                                                      .eq(ProductSku::getProductSkuCode, productSkuCode)));

                    if(ObjectUtil.isNull(productSku)){
                        throw new RuntimeException("商品不存在");
                    }
                    String stenantId = productSku.getTenantId();
                    try {
                        if(StrUtil.isEmpty(errorMessage)){
                            String logisticsCarrier = null;
                            if(ObjectUtil.isNotEmpty(bo.getLogisticsInfo())){
                                logisticsCarrier = bo.getLogisticsInfo().getLogisticsCarrier();
                            }
                            // 如果是测算/如果是会员
                            DeliveryFeeByErpResponse deliveryFeeByErp = priceSupportV2.getDeliveryFeeFromErp(stashList, bo.getAddressInfo()
                                                                                                                          .getZipCode(), tempOrder.getProductSkuCode(), Collections.singletonList(logisticsCarrier), dtenantId, productSku.getTenantId(),countryCode,zipCode );

                            if (ObjectUtil.isNotNull(deliveryFeeByErp)){
                                String currencyCode = deliveryFeeByErp.getCurrencyCode();
                                // 产品临时方案说抛出异常
                                if(ObjectUtil.isNotEmpty(currencyCode)&&!currencyCode.equals(tempOrder.getCurrencyCode())){
                                    throw new RuntimeException("货币不一致");
                                }
                                BigDecimal shippingFee = deliveryFeeByErp.getShippingFee();
                                ProductPriceResponse productPrice = priceSupportV2.getProductPrice(productSkuCode, shippingFee, dtenantId, siteId);
                                BigDecimal shippingFeeAfterDiscount = productPrice.getDeliveryFee();
                                BigDecimal basePrice = productPrice.getDistributorPickUpPrice();

                                LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>()
                                    .eq(Warehouse::getWarehouseCode, deliveryFeeByErp.getOrgWarehouseCode())
                                    .eq(Warehouse::getTenantId, stenantId);

                                Warehouse warehouse1 = TenantHelper.ignore(()->iWarehouseService.getOne(wrapper));
                                String warehouseSystemCode = warehouse1.getWarehouseSystemCode();

                                tempOrder.setWarehouseSystemCode(warehouseSystemCode);
                                tempOrder.setShippingFee(shippingFeeAfterDiscount);
                                tempOrder.setShippingTotalAmount(shippingFeeAfterDiscount.multiply(new BigDecimal(productQuantity)));
                                tempOrder.setPickUpPrice(basePrice);
                                tempOrder.setPickUpTotalAmount(basePrice.multiply(new BigDecimal(productQuantity)));
                                tempOrder.setDropShippingPrice(shippingFeeAfterDiscount.add(basePrice));
                                tempOrder.setDropShippingTotalAmount(shippingFeeAfterDiscount.add(basePrice).multiply(new BigDecimal(productQuantity)));
                            }
                        }
//                  提交时要放开
                    }catch (Exception e){
                        log.error("获取运费失败",e);
                        tempOrder.setShippingFee(null);
                        tempOrder.setShippingTotalAmount(null);
                        tempOrder.setWarehouseSystemCode(null);
                        tempOrder.setDropShippingPrice(null);
                        tempOrder.setDropShippingTotalAmount(null);
                    }

                }
            }

        } else if (StrUtil.equals(updateType, "LogisticsInfo")) {
            TempOrderUpdateBo.LogisticsInfo logisticsInfo = bo.getLogisticsInfo();
            Boolean thirdBilling = logisticsInfo.getLogisticsThirdBilling();
            String logisticsCarrier = logisticsInfo.getLogisticsCarrier();
            String logisticsServiceName = logisticsInfo.getLogisticsServiceName();
            String logisticsAccount = logisticsInfo.getLogisticsAccount();
            String logisticsAccountZipCode = logisticsInfo.getLogisticsAccountZipCode();
            String logisticsType = logisticsInfo.getLogisticsType();
            String warehouseCode = logisticsInfo.getWarehouseCode();
            Boolean isMarketplace = logisticsInfo.getIsMarketplace();
            List<String> logisticsTrackingNo = logisticsInfo.getLogisticsTrackingNo();


            ProductSku productSku;
            if (StrUtil.isNotBlank(warehouseCode)) {
                productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseCode);
                if (productSku == null) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE.args(productSkuCode, warehouseCode));
                }
            } else {
                productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            }

            // 是否第三方物流
            if (thirdBilling) {
                // 是否填写第三方物流账户
//                if (StrUtil.isBlank(logisticsAccount)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR);
//                }

//                // 是否填写第三方物流账户邮编
//                if (StrUtil.isBlank(logisticsAccountZipCode)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR);
//                }

                // 是否填写承运商
//                if (StrUtil.isBlank(logisticsCarrier)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
//                }

                // 仓库是否支持第三方发货
                if (StrUtil.isNotBlank(logisticsAccount)) {
                    //仓库是否支持第三方发货
                    if (StrUtil.isNotBlank(warehouseCode)) {
                        if (productSku != null) {
                            boolean support = iWarehouseService.isSupportThirdCarrier(warehouseCode);
                            if (!support) {
                                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_WAREHOUSE_NOT_SUPPORT_3RDBILLING_ERROR);
                            }
                        }
                    }
                }
            } else {
                // 是否填写承运商
//                if (StrUtil.isBlank(logisticsCarrier)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
//                }

                // 非第三方物流时，是否填写了物流单号
//                if (CollUtil.isEmpty(logisticsTrackingNo)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_TRACKING_NO_BLANK_ERROR);
//                }
            }

            if (Boolean.TRUE.equals(isMarketplace) && thirdBilling) {
                tempOrder.setShippingLabelOssId(null);
                tempOrder.setShippingLabelFileName(null);

                logisticsTrackingNo = null;
                OrderAttachment newOrderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
                if (newOrderAttachment != null) {
                    iOrderAttachmentService.removeById(newOrderAttachment);
                }
            }

            tempOrder.setTempOrderNo(tempOrderNo);
            tempOrder.setLogisticsThirdBilling(thirdBilling);
            tempOrder.setLogisticsCarrier(logisticsCarrier);
            tempOrder.setLogisticsServiceName(logisticsServiceName);
            tempOrder.setLogisticsAccount(logisticsAccount);
            tempOrder.setLogisticsAccountZipCode(logisticsAccountZipCode);

            if (!thirdBilling) {
                tempOrder.setLogisticsAccount(null);
                tempOrder.setLogisticsAccountZipCode(null);
            }

            tempOrder.setLogisticsType(LogisticsTypeEnum.valueOf(logisticsType));
            tempOrder.setWarehouseSystemCode(warehouseCode);
            if (CollUtil.isNotEmpty(logisticsTrackingNo)) {
                tempOrder.setLogisticsTrackingNo(JSONUtil.parseArray(logisticsTrackingNo));
            } else {
                tempOrder.setLogisticsTrackingNo(null);
            }
        } else if (StrUtil.equals(updateType, "ChannelInfo")) {
            TempOrderUpdateBo.ChannelInfo channelInfo = bo.getChannelInfo();
            String channelOrderNo = channelInfo.getStoreOrderId();
            // 查询此账户所有订单判断是否有重复的，排除Canceled的
            boolean orderExists = iOrdersService.existsChannelOrderNo(channelOrderNo, OrderStateType.Canceled);
            // 查询导入缓存表
            boolean tempOrderExists = iOrderImportTempService.existsChannelOrderNoExcludeSelf(channelOrderNo, tempOrderId);
            if (orderExists || tempOrderExists) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STORE_ORDER_ID_REPEAT);
            } else {
                tempOrder.setStoreName(StrUtil.emptyToNull(channelInfo.getStoreName()));
                tempOrder.setStoreOrderId(StrUtil.emptyToNull(channelOrderNo));
                tempOrder.setChannelType(channelInfo.getChannelType());
            }
        } else if (StrUtil.equals(updateType, "UploadShippingLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            // 通过url下载解析
            try {
                if(!CarrierTypeEnum.LTL.getValue().equals(carrier)){
                    checkLabelNum(0,tempOrder.getProductQuantity(),sysOssVo.getUrl());
                }
            }catch (Exception e){
                return R.fail(e.getMessage());
            }
            String originalName = saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.ShippingLabel, carrier,tempOrdersMap,orderExtendId);

            tempOrder.setShippingLabelOssId(sysOssVo.getOssId());
            tempOrder.setShippingLabelFileName(originalName);
        } else if (StrUtil.equals(updateType, "UploadBolLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.BOL, carrier,tempOrdersMap,orderExtendId);
            tempOrder.setBolOssId(sysOssVo.getOssId());

        } else if (StrUtil.equals(updateType, "UploadCartonLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.CartonLabel, carrier,tempOrdersMap,orderExtendId);
            JSONObject ids ;
            if(CarrierTypeEnum.LTL.name().equals(carrier)){
                ids = JSONObjectUtils.putValueToCollByKey(cartonlabelOssIds,"ids",sysOssVo.getOssId());
            }else {
                ids = JSONObjectUtils.putValueToCollByKey(null,"ids",sysOssVo.getOssId());
            }

            tempOrder.setCartonLabelOssIds(ids);
        } else if (StrUtil.equals(updateType, "UploadPalletLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.PalletLabel, carrier,tempOrdersMap,orderExtendId);
            JSONObject ids ;
            if(CarrierTypeEnum.LTL.name().equals(carrier)){
                ids = JSONObjectUtils.putValueToCollByKey(palletlabelOssIds,"ids",sysOssVo.getOssId());
            }else {
                ids = JSONObjectUtils.putValueToCollByKey(null,"ids",sysOssVo.getOssId());
            }
            // 如果是非LTL的 ids 只用sysOssVo.getOssId()
            tempOrder.setPalletLabelOssIds(ids);
        } else if (StrUtil.equals(updateType, "UploadItemLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.ItemLabel, carrier,tempOrdersMap,orderExtendId);
            JSONObject ids ;
            if(CarrierTypeEnum.LTL.name().equals(carrier)){
                ids = JSONObjectUtils.putValueToCollByKey(itemlabelOssIds,"ids",sysOssVo.getOssId());
            }else {
                ids = JSONObjectUtils.putValueToCollByKey(null,"ids",sysOssVo.getOssId());
            }
            tempOrder.setItemLabelOssIds(ids);

        } else if (StrUtil.equals(updateType, "UploadOther")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            String fileSuffix = sysOssVo.getFileSuffix();
//            .xlsx  .xlsm .xlsb .xls .csv
            if(".xlsx".equals(fileSuffix)||".xlsm".equals(fileSuffix)||".xlsb".equals(fileSuffix)||".xls".equals(fileSuffix)||".csv".equals(fileSuffix)){

            }else {
//                Other附件格式不正确，附件仅支持excel 文件
                return R.fail(ZSMallStatusCodeEnum.ONLY_EXCEL_IS_SUPPORTED);

            }
            saveTheSingleAttachmentAfterDeletion(sysOssVo, orderNo,OrderAttachmentTypeEnum.Other, carrier,tempOrdersMap,orderExtendId);
            JSONObject ids = JSONObjectUtils.putValueToCollByKey(otherOssIds,"ids",sysOssVo.getOssId());
            tempOrder.setOtherOssIds(ids);

        } else if (StrUtil.equals(updateType, "DeleteBolLabel")) {
            if(CarrierTypeEnum.LTL.name().equals(carrier)){
                iOrderAttachmentService.deleteByOrderNosAndAttachmentType(tempOrdersMap.get(orderExtendId), OrderAttachmentTypeEnum.BOL);
            }else {
                iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.BOL);
            }
            tempOrder.setBolOssId(null);

        } else if (StrUtil.equals(updateType, "DeleteCartonLabel")) {
            iOrderAttachmentService.deleteByOrderNosAndOssId(tempOrdersMap.get(orderExtendId), attachmentOssId);
            JSONObject entries = JSONObjectUtils.removeOssIdFromArray(tempOrder.getCartonLabelOssIds(), "ids", attachmentOssId);
            tempOrder.setCartonLabelOssIds(entries);
        } else if (CharSequenceUtil.equals(updateType, "DeletePalletLabel")) {
            iOrderAttachmentService.deleteByOrderNosAndOssId(tempOrdersMap.get(orderExtendId), attachmentOssId);
            JSONObject entries = JSONObjectUtils.removeOssIdFromArray(tempOrder.getPalletLabelOssIds(), "ids", attachmentOssId);
            tempOrder.setPalletLabelOssIds(entries);

        } else if (CharSequenceUtil.equals(updateType, "DeleteItemLabel")) {
            iOrderAttachmentService.deleteByOrderNosAndOssId(tempOrdersMap.get(orderExtendId), attachmentOssId);
            JSONObject entries = JSONObjectUtils.removeOssIdFromArray(tempOrder.getItemLabelOssIds(), "ids", attachmentOssId);
            tempOrder.setItemLabelOssIds(entries);

        } else if (CharSequenceUtil.equals(updateType, "DeleteOther")) {
            iOrderAttachmentService.deleteByOrderNosAndOssId(tempOrdersMap.get(orderExtendId), attachmentOssId);
            JSONObject entries = JSONObjectUtils.removeOssIdFromArray(tempOrder.getOtherOssIds(), "ids", attachmentOssId);
            tempOrder.setOtherOssIds(entries);

        }else if (CharSequenceUtil.equals(updateType, "DeleteShippingLabel")) {
            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
            tempOrder.setShippingLabelOssId(null);
            tempOrder.setShippingLabelFileName(null);
        } else if (CharSequenceUtil.equals(updateType, "UploadOrderAttachment")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }

            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            String originalName = sysOssVo.getOriginalName();
            String suffix = FileUtil.getSuffix(originalName);

            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setOrderNo(orderNo);
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.Zip);

            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
            iOrderAttachmentService.save(newOrderAttachment);
        } else if (StrUtil.equals(updateType, "DeleteOrderAttachment")) {
            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
        }
        // 更新临时订单 ,如果订单派送费异常,相关数据置为null  如果是地址信息的变更,先修改基础信息
        if(CarrierTypeEnum.LTL.getValue().equals(carrier)){
            // 查查是不是存在合单的情况
            List<OrderImportTemp> orderImportTemps = iOrderImportTempService.queryByOrderExtendId(orderExtendId);
            // 先更新基础信息--如果不是修改附件不需要批量修改
            if(CollUtil.isNotEmpty(orderImportTemps)){
                if(StrUtil.equalsAny(updateType,"AddressInfo","LogisticsInfo","ChannelInfo")){
                    iOrderImportTempService.updateById(tempOrder);
                }else {
                    if(orderImportTemps.size()>1){
                        orderImportTemps.stream()
//                                .filter(o -> !Objects.equals(o.getOrderNo(), tempOrder.getOrderNo()))
                                        .forEach(o -> copyOssProperties(o, tempOrder));
                        iOrderImportTempService.updateBatchById(orderImportTemps);

                        if(ObjectUtil.isEmpty(tempOrder.getShippingFee())){
                            iOrderImportTempService.updateBatchSetNull(tempOrder.getOrderExtendId());
                        }
                    }else {
                        iOrderImportTempService.updateById(tempOrder);
                    }
                }
            }
        }else {
            iOrderImportTempService.updateById(tempOrder);
            if(ObjectUtil.isEmpty(tempOrder.getShippingFee())){
                iOrderImportTempService.updateSetNull(tempOrder);
            }
        }


        if(StrUtil.isNotBlank(errorMessage)){
            return R.fail(errorMessage);
        }
        return R.ok();
    }
    private void copyOssProperties(OrderImportTemp target, OrderImportTemp source) {
        target.setCartonLabelOssIds(source.getCartonLabelOssIds());
        target.setOtherOssIds(source.getOtherOssIds());
        target.setItemLabelOssIds(source.getItemLabelOssIds());
        target.setPalletLabelOssIds(source.getPalletLabelOssIds());
        target.setBolOssId(source.getBolOssId());
    }
    /**
     * 功能描述：删除后保存单个附件
     * 支持附件限制一个的删除和保存
     *
     * @param sysOssVo         sys oss vo
     * @param orderNo          订单号
     * @param label            标签
     * @param logisticsCarrier
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/05/19
     */
    private String saveTheSingleAttachmentAfterDeletion(SysOssVo sysOssVo, String orderNo, OrderAttachmentTypeEnum label,
                                                        String logisticsCarrier, Map<String, List<String>> orderExtendIds,String orderExtendId) {
        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);


        // 如果是bol的,且是LTL的需要根据行id做统一保存和统一删除
        if(CarrierTypeEnum.LTL.getValue().equals(logisticsCarrier)){
            List<OrderAttachment> newOrderAttachments = getOrderAttachments(sysOssVo, label, orderExtendIds, orderExtendId, originalName, suffix);
            if (OrderAttachmentTypeEnum.CartonLabel.equals(label) || OrderAttachmentTypeEnum.PalletLabel.equals(label) || OrderAttachmentTypeEnum.ItemLabel.equals(label) || OrderAttachmentTypeEnum.Other.equals(label)) {
                iOrderAttachmentService.saveBatch(newOrderAttachments);
            } else if (OrderAttachmentTypeEnum.BOL.equals(label)) {
                iOrderAttachmentService.deleteByOrderNosAndAttachmentType(orderExtendIds.get(orderExtendId), label);
                iOrderAttachmentService.saveBatch(newOrderAttachments);
            }
        }else {
            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setOrderNo(orderNo);
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            newOrderAttachment.setAttachmentType(label);
            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, label);
            iOrderAttachmentService.save(newOrderAttachment);
        }

        return originalName;
    }

    @NotNull
    private List<OrderAttachment> getOrderAttachments(SysOssVo sysOssVo, OrderAttachmentTypeEnum label,
                                                      Map<String, List<String>> orderExtendIds, String orderExtendId,
                                                      String originalName, String suffix) {
        List<String> orderNos = orderExtendIds.get(orderExtendId);

        List<OrderAttachment> newOrderAttachments = new ArrayList<>();
        for (String orderNo : orderNos) {
            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setOrderNo(orderNo);
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            newOrderAttachment.setAttachmentType(label);
            newOrderAttachments.add(newOrderAttachment);
        }
        return newOrderAttachments;
    }

    /**
     * 功能描述：检查标签与货物数量是否一致
     *
     * @param code     代码
     * @param quantity 数量
     * @param url      网址
     * <AUTHOR>
     * @date 2025/05/16
     */
    public void checkLabelNum(Integer code,Integer quantity,String url){

        if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(code)){
            // todo 线上放开 String pdfUrl = ImagesUtil.downloadPdf(url);
            InputStream shippingLabel ;
            try {
                shippingLabel = ImagesUtil.downloadPdfAsStream(url);
            } catch (Exception e) {
                throw new RuntimeException("File download failure!");
            }

            List<String> base64List = null;
            try {
                base64List = PDFUtil.splitPdfToBase64(shippingLabel, 1);
            } catch (Exception e) {
                throw new RuntimeException("Failed to split the PDF file!");
            }
            if(CollUtil.isEmpty(base64List)||quantity!=base64List.size()){
                throw new ShippingValidationException("shippinglabel 与SKU 数量不匹配");
            }
        }
    }
    @Override
    public R<Void> updateTempOrder(TempOrderUpdateV2Bo bo, String updateType) throws Exception {
        String tempOrderNo = bo.getTempOrderNo();
        String errorMessage = null;
        String dtenantId = LoginHelper.getTenantId();
        if (StrUtil.isBlank(tempOrderNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        OrderImportTemp tempOrder = iOrderImportTempService.queryByTempOrderNoAndTempState(tempOrderNo, OrderTempState.Temporary);
        String carrier = tempOrder.getLogisticsCarrier();
        String orderExtendId = tempOrder.getOrderExtendId();
        String productSkuCode = tempOrder.getProductSkuCode();
        Integer productQuantity = tempOrder.getProductQuantity();
        if (tempOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }
        Long tempOrderId = tempOrder.getId();
        String orderNo = tempOrder.getOrderNo();
        Boolean postCodeChange = false;
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);

        /**
         * 更新类型：
         * AddressInfo-地址信息，
         * LogisticsInfo-物流信息，
         * ChannelInfo-渠道信息，
         * UploadShippingLabel-上传快递标签，
         * DeleteShippingLabel-删除快递标签，
         * UploadOrderAttachment-上传订单附件
         * DeleteOrderAttachment-删除订单附件
         */
        if (StrUtil.equals(updateType, "AddressInfo")) {
            //如果是代发
            if (ObjectUtil.equal(tempOrder.getLogisticsType(), LogisticsTypeEnum.DropShipping)) {
                //判断更新邮编不能为空
                if (ObjectUtil.isNull(bo.getZipCode())) {
                    return R.fail("地址邮编不能为空");
                }
                //判断邮编是否改变且是代发订单
                if (!ObjectUtil.equal(tempOrder.getZipCode(), bo.getZipCode())) {
                    postCodeChange = true;
                }
            }

            String recipientName = bo.getRecipientName();
            String phoneNumber = bo.getPhoneNumber();
            String address1 = bo.getAddress1();
            String address2 = bo.getAddress2();
            String address3 = bo.getAddress3();
            String city = bo.getCity();
            Long countryId = bo.getCountryId();
            Long stateId = bo.getStateId();
            String stateCode = bo.getState();

            String zipCode = bo.getZipCode();

            WorldLocationVo country = iWorldLocationService.queryById(countryId);
            String countryCode = country.getLocationCode();
            Long siteId = null;
            siteId = iSiteCountryCurrencyService.getSiteIdByCountryCode(countryCode);
            if (stateId != null) {
                WorldLocationVo state = iWorldLocationService.queryById(stateId);
                if (ObjectUtil.isNotNull(state)) {
                    stateCode = state.getLocationCode();
                }
            }

            String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
            // 支持的国家
            List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
            // 支持的州
            List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");

            if (StrUtil.isBlank(recipientName)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_RECIPIENT_NAME_BLANK_ERROR);
            }
            if (StrUtil.isBlank(phoneNumber)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_BLANK_ERROR);
            }
            if (StrUtil.isBlank(address1)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ADDRESS1_BLANK_ERROR);
            }
            if (StrUtil.isBlank(city)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CITY_BLANK_ERROR);
            }

            // 校验手机号
            if (StrUtil.isNotBlank(phoneNumber) && StrUtil.length(phoneNumber) > 80) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_ERROR);
            }

            // 校验国家
            if (StrUtil.equals(countryCode, "US")) {
                if (ObjectUtil.equals(countryId, 0L)) {
                    return R.fail(ZSMallStatusCodeEnum.UNSUPPORTED_COUNTRIES);
                }

                if (StrUtil.isBlank(zipCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_BLANK_ERROR);
                }

                // 仅US的才校验州
                if (!SUPPORT_STATE.contains(stateCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STATE_NOT_EXIST);
                }
                // 仅US的才校验邮编
                if (!RegexUtil.matchUSPostalCode(zipCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_NOT_EXIST);
                }
            } else if (!SUPPORT_COUNTRY.contains(countryCode)) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_COUNTRY_NOT_EXIST);
            }

            tempOrder.setRecipientName(recipientName);
            tempOrder.setPhoneNumber(phoneNumber);
            tempOrder.setAddress1(address1);
            tempOrder.setAddress2(address2);
            tempOrder.setAddress3(address3);
            tempOrder.setCity(city);
            tempOrder.setStateCode(stateCode);
            tempOrder.setCountryCode(countryCode);
            tempOrder.setZipCode(zipCode);
//            tempOrder.setCurrencySymbol();

            if (approvedTenant) {
                if (postCodeChange) {
                    // 实际可能为null 实际为测算异常
                    List<String> stashList = null;
                    try {
                        stashList = getStashList(productSkuCode, productQuantity, countryCode);
                    } catch (Exception e) {
                        errorMessage = "库存不足,获取派送费失败";
                        log.error("获取运费失败", e);
                        log.error("获取运费失败", e);
                        tempOrder.setShippingFee(null);
                        tempOrder.setShippingTotalAmount(null);
                        tempOrder.setWarehouseSystemCode(null);
                        tempOrder.setDropShippingPrice(null);
                        tempOrder.setDropShippingTotalAmount(null);
                    }

                    ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                                                                        .eq(ProductSku::getProductSkuCode, productSkuCode)));

                    if (ObjectUtil.isNull(productSku)) {
                        throw new RuntimeException("商品不存在");
                    }
                    String stenantId = productSku.getTenantId();
                    try {
                        if (StrUtil.isEmpty(errorMessage)) {
                            String logisticsCarrier = null;
                            if (ObjectUtil.isNotEmpty(bo)) {
                                logisticsCarrier = bo.getLogisticsCarrier();
                            }
                            // 如果是测算/如果是会员 todo 改造加入国家 和邮编 postcode
                            DeliveryFeeByErpResponse deliveryFeeByErp = priceSupportV2.getDeliveryFeeFromErp(stashList, bo
                                .getZipCode(), tempOrder.getProductSkuCode(), Collections.singletonList(logisticsCarrier), dtenantId, productSku.getTenantId(),countryCode
                                , zipCode);

                            if (ObjectUtil.isNotNull(deliveryFeeByErp)) {
                                BigDecimal shippingFee = deliveryFeeByErp.getShippingFee();
                                ProductPriceResponse productPrice = priceSupportV2.getProductPrice(productSkuCode, shippingFee, dtenantId,siteId );
                                BigDecimal shippingFeeAfterDiscount = productPrice.getDeliveryFee();
                                BigDecimal basePrice = null;
                                if(productPrice.getIsMember()){
                                    basePrice = productPrice.getDistributorMemberPickUpPrice();
                                }else {
                                    basePrice = productPrice.getDistributorPickUpPrice();
                                }

                                LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>()
                                    .eq(Warehouse::getWarehouseCode, deliveryFeeByErp.getOrgWarehouseCode())
                                    .eq(Warehouse::getTenantId, stenantId);

                                Warehouse warehouse1 = TenantHelper.ignore(() -> iWarehouseService.getOne(wrapper));
                                String warehouseSystemCode = warehouse1.getWarehouseSystemCode();
                                tempOrder.setWarehouseSystemCode(warehouseSystemCode);
                                tempOrder.setShippingFee(shippingFeeAfterDiscount);
                                tempOrder.setShippingTotalAmount(shippingFeeAfterDiscount.multiply(new BigDecimal(productQuantity)));
                                tempOrder.setPickUpPrice(basePrice);
                                tempOrder.setPickUpTotalAmount(basePrice.multiply(new BigDecimal(productQuantity)));
                                tempOrder.setDropShippingPrice(shippingFeeAfterDiscount.add(basePrice));
                                tempOrder.setDropShippingTotalAmount(shippingFeeAfterDiscount.add(basePrice)
                                                                                             .multiply(new BigDecimal(productQuantity)));
                            }
                        }
//                  提交时要放开
                    } catch (Exception e) {
                        log.error("获取运费失败", e);
                        tempOrder.setShippingFee(null);
                        tempOrder.setShippingTotalAmount(null);
                        tempOrder.setWarehouseSystemCode(null);
                        tempOrder.setDropShippingPrice(null);
                        tempOrder.setDropShippingTotalAmount(null);
                    }

                }
            }

        } else if (StrUtil.equals(updateType, "LogisticsInfo")) {

            Boolean thirdBilling = bo.getLogisticsThirdBilling();
            String logisticsCarrier = bo.getLogisticsCarrier();
            String logisticsServiceName = bo.getLogisticsServiceName();
            String logisticsAccount = bo.getLogisticsAccount();
            String logisticsAccountZipCode = bo.getLogisticsAccountZipCode();
            String logisticsType = bo.getLogisticsType();
            String warehouseCode = bo.getWarehouseCode();
            Boolean isMarketplace = bo.getIsMarketplace();
            List<String> logisticsTrackingNo = bo.getLogisticsTrackingNo();


            ProductSku productSku;
            if (StrUtil.isNotBlank(warehouseCode)) {
                productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseCode);
                if (productSku == null) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE.args(productSkuCode, warehouseCode));
                }
            } else {
                productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            }

            // 是否第三方物流
            if (thirdBilling) {
                // 是否填写第三方物流账户
                if (StrUtil.isBlank(logisticsAccount)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR);
                }

                // 是否填写第三方物流账户邮编
                if (StrUtil.isBlank(logisticsAccountZipCode)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR);
                }

                // 是否填写承运商
                if (StrUtil.isBlank(logisticsCarrier)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
                }

                // 仓库是否支持第三方发货
                if (StrUtil.isNotBlank(logisticsAccount)) {
                    //仓库是否支持第三方发货
                    if (StrUtil.isNotBlank(warehouseCode)) {
                        if (productSku != null) {
                            boolean support = iWarehouseService.isSupportThirdCarrier(warehouseCode);
                            if (!support) {
                                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_WAREHOUSE_NOT_SUPPORT_3RDBILLING_ERROR);
                            }
                        }
                    }
                }
            } else {
                // 是否填写承运商
                if (StrUtil.isBlank(logisticsCarrier)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
                }

                // 非第三方物流时，是否填写了物流单号
                if (CollUtil.isEmpty(logisticsTrackingNo)) {
                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_TRACKING_NO_BLANK_ERROR);
                }
            }

            if (Boolean.TRUE.equals(isMarketplace) && thirdBilling) {
                tempOrder.setShippingLabelOssId(null);
                tempOrder.setShippingLabelFileName(null);

                logisticsTrackingNo = null;
                OrderAttachment newOrderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
                if (newOrderAttachment != null) {
                    iOrderAttachmentService.removeById(newOrderAttachment);
                }
            }

            tempOrder.setTempOrderNo(tempOrderNo);
            tempOrder.setLogisticsThirdBilling(thirdBilling);
            tempOrder.setLogisticsCarrier(logisticsCarrier);
            tempOrder.setLogisticsServiceName(logisticsServiceName);
            tempOrder.setLogisticsAccount(logisticsAccount);
            tempOrder.setLogisticsAccountZipCode(logisticsAccountZipCode);

            if (!thirdBilling) {
                tempOrder.setLogisticsAccount(null);
                tempOrder.setLogisticsAccountZipCode(null);
            }

            tempOrder.setLogisticsType(LogisticsTypeEnum.valueOf(logisticsType));
            tempOrder.setWarehouseSystemCode(warehouseCode);
            if (CollUtil.isNotEmpty(logisticsTrackingNo)) {
                tempOrder.setLogisticsTrackingNo(JSONUtil.parseArray(logisticsTrackingNo));
            } else {
                tempOrder.setLogisticsTrackingNo(null);
            }
        } else if (StrUtil.equals(updateType, "ChannelInfo")) {

            String channelOrderNo = bo.getStoreOrderId();
            // 查询此账户所有订单判断是否有重复的，排除Canceled的
            boolean orderExists = iOrdersService.existsChannelOrderNo(channelOrderNo, OrderStateType.Canceled);
            // 查询导入缓存表
            boolean tempOrderExists = iOrderImportTempService.existsChannelOrderNoExcludeSelf(channelOrderNo, tempOrderId);
            if (orderExists || tempOrderExists) {
                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STORE_ORDER_ID_REPEAT);
            } else {
                tempOrder.setStoreName(StrUtil.emptyToNull(bo.getStoreName()));
                tempOrder.setStoreOrderId(StrUtil.emptyToNull(channelOrderNo));
                tempOrder.setChannelType(bo.getChannelType());
            }
        } else if (StrUtil.equals(updateType, "UploadShippingLabel")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }

            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            String originalName = sysOssVo.getOriginalName();
            String suffix = FileUtil.getSuffix(originalName);

            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setOrderNo(orderNo);
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);

            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
            iOrderAttachmentService.save(newOrderAttachment);

            tempOrder.setShippingLabelOssId(sysOssVo.getOssId());
            tempOrder.setShippingLabelFileName(originalName);
        } else if (StrUtil.equals(updateType, "DeleteShippingLabel")) {
            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
            tempOrder.setShippingLabelOssId(null);
            tempOrder.setShippingLabelFileName(null);
        } else if (StrUtil.equals(updateType, "UploadOrderAttachment")) {
            MultipartFile multipartFile = bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }

            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
            String originalName = sysOssVo.getOriginalName();
            String suffix = FileUtil.getSuffix(originalName);

            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setOrderNo(orderNo);
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.Zip);

            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
            iOrderAttachmentService.save(newOrderAttachment);
        } else if (StrUtil.equals(updateType, "DeleteOrderAttachment")) {
            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
        }
        if(CarrierTypeEnum.LTL.getValue().equals(carrier)){
            // 查查是不是存在合单的情况
            List<OrderImportTemp> orderImportTemps = iOrderImportTempService.queryByOrderExtendId(orderExtendId);
            if(CollUtil.isNotEmpty(orderImportTemps)&&orderImportTemps.size()>1){
                // 这个入口是只改address的
//                orderImportTemps.stream()
//                                .filter(o -> !Objects.equals(o.getOrderNo(), tempOrder.getOrderNo()))
//                                .forEach(o -> copyOssProperties(o, tempOrder));
//                iOrderImportTempService.updateBatchById(orderImportTemps);

                if(ObjectUtil.isEmpty(tempOrder.getShippingFee())){
                    iOrderImportTempService.updateBatchSetNull(orderExtendId);
                }
            }else {
                iOrderImportTempService.updateById(tempOrder);
                if(ObjectUtil.isEmpty(tempOrder.getShippingFee())){
                    iOrderImportTempService.updateSetNull(tempOrder);
                }
            }
        }else {
            iOrderImportTempService.updateById(tempOrder);
            if(ObjectUtil.isEmpty(tempOrder.getShippingFee())){
                iOrderImportTempService.updateSetNull(tempOrder);
            }
        }
        if (StrUtil.isNotBlank(errorMessage)) {
            return R.fail(errorMessage);
        }
        return R.ok();
    }
    /**
     * 功能描述：获取库存清单,如果这个接口报错,大概率是没有库存,此时要清空订单价格 ,导入和下单专用
     *
     * @param productSkuCode 产品sku代码
     * @param quantity       数量
     * @param countryCode
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/08/20
     */
    public List<String> getStashList(String productSkuCode, Integer quantity, String countryCode) {

        LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
            .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
            .ge(ProductSkuStock::getStockAvailable, quantity)
            .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
            .eq(ProductSkuStock::getDelFlag, 0);
        List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
        List<String> stashCodes1 = null;
        if(CollUtil.isNotEmpty(list)){
            List<String> stashCodes = list.stream().map(ProductSkuStock::getWarehouseSystemCode).collect(Collectors.toList());
            LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, stashCodes);
            List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));

            stashCodes1 = iWarehouseDeliveryCountryService.filterWarehouseIds(list1,countryCode);

        }else {
            LambdaQueryWrapper<ProductSkuStock> eq2 = new LambdaQueryWrapper<ProductSkuStock>()
                .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
                .eq(ProductSkuStock::getDelFlag, 0);
            List<ProductSkuStock> list2 = TenantHelper.ignore(()->iProductSkuStockService.list(eq2));
            List<String> stashCodes2 = null;
            if(CollUtil.isNotEmpty(list2)) {
                List<String> stashCodes = list2.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                               .collect(Collectors.toList());
                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>()
                    .in(Warehouse::getWarehouseSystemCode, stashCodes)
                    .orderByDesc(Warehouse::getCreateTime)
                    .orderByDesc(Warehouse::getId);
                List<Warehouse> list1 = TenantHelper.ignore(() -> iWarehouseService.list(in));
                // todo 仓库校验前置
                stashCodes2 = iWarehouseDeliveryCountryService.filterWarehouseIds(list1,countryCode);
//            throw new RuntimeException("库存不足");
                if(CollUtil.isEmpty(stashCodes2)){
                    throw new RuntimeException("库存不足");
                }
                return stashCodes2;
            }
        }
        if(CollUtil.isEmpty(stashCodes1)){
            throw new RuntimeException("库存不足");
        }
        return stashCodes1;
    }
    /**
     * 更新购物车临时订单
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> updateShippingCartTempOrder(ShippingCartTempOrderUpdateBo bo) {
        String tempOrderNo = bo.getTempOrderNo();
        String activityCode = bo.getActivityCode();
        String logisticsType = bo.getLogisticsType();
        Integer quantity = bo.getQuantity();

        OrderImportTemp tempOrder = iOrderImportTempService.queryByTempOrderNoAndTempState(tempOrderNo, OrderTempState.Temporary);
        if (tempOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }

        String productSkuCode = tempOrder.getProductSkuCode();

        if (activityCode != null) {
            if (StrUtil.isBlank(activityCode)) {
                tempOrder.setActivityCode(null);
            } else {
                ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(
                    ProductActivityItem.builder()
                        .activityCode(activityCode)
                        .activityState(ProductActivityItemStateEnum.InProgress)
                        .productSkuCode(productSkuCode).build()
                );

                if (ObjectUtil.isNull(activityItem)) {
                    return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
                }
                tempOrder.setActivityCode(activityCode);
            }
        } else if (StrUtil.isNotBlank(logisticsType)) {
            Product product = iProductService.queryByProductSkuCode(productSkuCode);
            if (product == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF.args(productSkuCode));
            }

            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
            if (StrUtil.equals(logisticsType, LogisticsTypeEnum.PickUp.name())) {
                if (ObjectUtil.equals(supportedLogistics, SupportedLogisticsEnum.DropShippingOnly)) {
                    //不能自提
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_ONLY_SUPPORT_DROPSHIPPING);
                }

                tempOrder.setLogisticsType(LogisticsTypeEnum.PickUp);
                tempOrder.setLogisticsThirdBilling(false);
                if (StrUtil.isNotBlank(tempOrder.getLogisticsAccount())) {
                    tempOrder.setLogisticsThirdBilling(true);
                }
            } else if (StrUtil.equals(logisticsType, LogisticsTypeEnum.DropShipping.name())) {
                if (ObjectUtil.equals(supportedLogistics, SupportedLogisticsEnum.PickUpOnly)) {
                    //不能代发
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_ONLY_SUPPORT_PICK_UP);
                }

                tempOrder.setLogisticsCarrier(null);
                tempOrder.setLogisticsType(LogisticsTypeEnum.DropShipping);
                tempOrder.setLogisticsAccount(null);
                tempOrder.setLogisticsServiceName(null);
                tempOrder.setLogisticsThirdBilling(false);
                tempOrder.setLogisticsAccountZipCode(null);
                tempOrder.setLogisticsTrackingNo(null);
                tempOrder.setShippingLabelFileName(null);
                tempOrder.setShippingLabelOssId(null);
                tempOrder.setBolOssId(null);
                tempOrder.setCartonLabelOssIds(null);
                tempOrder.setItemLabelOssIds(null);
                tempOrder.setPalletLabelOssIds(null);
                tempOrder.setOtherOssIds(null);

                iOrderAttachmentService.deleteByOrderNoAndAttachmentType(tempOrder.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
            }
        }

        BigDecimal platformPickUpPrice;
        BigDecimal platformDropShippingPrice;
        if (StrUtil.isNotBlank(tempOrder.getActivityCode())) {  // 活动订单，需要查询活动价格
            ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCode(tempOrder.getActivityCode());
            // 因为分销商参加活动时已经支付了定金，所以此处不能直接取自提价/代发价，只能取尾款单价
            platformPickUpPrice = activityPriceItem.getPlatformBalanceUnitPrice();
            // 代发价等于尾款单价加上尾程派送费
            platformDropShippingPrice = NumberUtil.add(platformPickUpPrice, activityPriceItem.getPlatformFinalDeliveryFee());
        } else {  // 普通订单，直接查询SKU原价
            // 先判断是否绑定定价公式
            ProductSkuPriceRuleRelation relation = iProductSkuPriceRuleRelationService.getByProductSkuCode(productSkuCode);
            if (relation == null) {
                return R.fail(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
            }

//            ProductSkuPrice skuPrice = iProductSkuPriceService.queryByProductSkuCode(productSkuCode);
//            platformPickUpPrice = skuPrice.getPlatformPickUpPrice();
//            platformDropShippingPrice = skuPrice.getPlatformDropShippingPrice();
        }
            // 如果数量发生了变化,需要重新更新价格
        if(ObjectUtil.isNotEmpty(quantity)&& !Objects.equals(quantity, tempOrder.getProductQuantity())){
            //判断数量是否合法
            SkuStock skuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSkuStock(tempOrder.getProductSkuCode()));
            if (ObjectUtil.isNotNull(skuStock)){
                if (ObjectUtil.equal(tempOrder.getLogisticsType(),LogisticsTypeEnum.PickUp)){
                    if (tempOrder.getProductQuantity()>skuStock.getPickUpStockTotal()){
                        return R.fail(ZSMallStatusCodeEnum.COMMODITY_QUANTITY_OVERSHOOT);
                    }
                } else if (ObjectUtil.equal(tempOrder.getLogisticsType(),LogisticsTypeEnum.DropShipping)){
                    if (tempOrder.getProductQuantity()>skuStock.getDropShippingStockTotal()){
                        return R.fail(ZSMallStatusCodeEnum.COMMODITY_QUANTITY_OVERSHOOT);
                    }
                }
            }else {
                return R.fail(ZSMallStatusCodeEnum.COMMODITY_QUANTITY_OVERSHOOT);
            }

//            重新计算价格
            tempOrder.setProductQuantity(quantity);
            tempOrder.setPickUpTotalAmount(NumberUtil.mul(tempOrder.getPickUpPrice(), quantity));
            if(ObjectUtil.isNotEmpty(tempOrder.getDropShippingPrice())){
                tempOrder.setDropShippingTotalAmount(NumberUtil.mul(tempOrder.getDropShippingPrice(), quantity));
            }else {
                tempOrder.setDropShippingTotalAmount(null);
            }

        }
//        tempOrder.setPickUpPrice(platformPickUpPrice);
//        tempOrder.setPickUpTotalAmount(NumberUtil.mul(platformPickUpPrice, tempOrder.getProductQuantity()));
//        tempOrder.setDropShippingPrice(platformDropShippingPrice);
//        tempOrder.setDropShippingTotalAmount(NumberUtil.mul(platformDropShippingPrice, tempOrder.getProductQuantity()));
        iOrderImportTempService.updateById(tempOrder);
        return R.ok();
    }

    /**
     * 批量上传快递标签
     *
     * @param fileList
     * @return
     */
    @Override
    public R<Void> batchUploadShippingLabel(String recordNo, List<MultipartFile> fileList) {
        OrderImportRecord orderImportRecord = iOrderImportRecordService.queryByRecordNo(recordNo);
        if (orderImportRecord == null) {
            return R.fail(ZSMallStatusCodeEnum.IMPORT_RECORD_NOT_FOUND);
        }

        Long recordId = orderImportRecord.getId();
        StrBuilder batchUploadTips = StrBuilder.create(MessageUtils.message("zsmall.orders.batchUploadShippingLabelTips"));

        String rowSuccessTemplate = "<p><span style='color: green'>[{}]{}</span></p>";
        String rowErrorTemplate = "<p><span style='color: red'>[{}]{}</span></p>";

        List<OrderAttachment> attachmentList = new ArrayList<>();
        List<String> orderNos = new ArrayList<>();

        List<String> tempList = new ArrayList<>();
        List<OrderImportTemp> orderImportTemps = new ArrayList<>();
        BatchUploadFile batchUploadFile = new BatchUploadFile();
        Map<String, MultipartFile> fileMap = new HashMap<>();
        for (MultipartFile file : fileList){
            String fileName = file.getOriginalFilename();
            if (StrUtil.contains(fileName, ".PDF")) {
                fileName = StrUtil.replace(fileName, ".PDF", ".pdf");
            }
            String storeOrderId = StrUtil.removeSuffix(fileName, ".pdf");
            tempList.add(storeOrderId);
            fileMap.put(storeOrderId,file);

        }
        batchUploadFile.setFileMap(fileMap);
        OSSUploadBatchEvent ossUploadEvent = new OSSUploadBatchEvent(batchUploadFile);
        SpringUtils.context().publishEvent(ossUploadEvent);
        Map<String, SysOssVo> sysOssVoMap = ossUploadEvent.getSysOssVoMap();

        List<OrderImportTemp> tempOrders = iOrderImportTempService.queryByStoreOrderIdsAndTempState(recordId, tempList, OrderTempState.Temporary);

        if(CollUtil.isEmpty(tempOrders)){
            throw new RuntimeException("临时订单异常");
        }
        Map<String, List<OrderImportTemp>> importTempMap = tempOrders.stream()
                                                                     .collect(Collectors.groupingBy(OrderImportTemp::getStoreOrderId));
        for (MultipartFile file : fileList) {
            String fileName = file.getOriginalFilename();
            if (StrUtil.contains(fileName, ".PDF")) {
                fileName = StrUtil.replace(fileName, ".PDF", ".pdf");
            }
            // 此标识-ltl单子不唯一
            String storeOrderId = StrUtil.removeSuffix(fileName, ".pdf");

            try {
                List<OrderImportTemp> tempOrderList= importTempMap.get(storeOrderId);
                if (CollUtil.isEmpty(tempOrderList)) {
                    throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelNotMatch"));
                }
                for (OrderImportTemp tempOrder : tempOrderList) {
                    String orderNo = tempOrder.getOrderNo();
                    LogisticsTypeEnum logisticsType = tempOrder.getLogisticsType();
                    String logisticsAccount = tempOrder.getLogisticsAccount();
                    String logisticsCarrier = tempOrder.getLogisticsCarrier();
                    // 2025-6-4 产品规则 LTL且PickUp,批量上传过滤
                    if(CarrierTypeEnum.LTL.name().equals(logisticsCarrier)&&LogisticsTypeEnum.PickUp.equals(tempOrder.getLogisticsType())){
                        continue;
                    }
                    // 填写了第三方账号，无需上传快递标签
                    if (StrUtil.isNotBlank(logisticsAccount)) {
                        throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelNotMatch"));
                    }

                    // 不是自提订单直接跳过
                    if (!LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                        throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelNotMatch"));
                    }

                    SysOssVo sysOssVo = sysOssVoMap.get(storeOrderId);
                    String originalName = sysOssVo.getOriginalName();
                    String suffix = FileUtil.getSuffix(originalName);
                    try {
                        if(!CarrierTypeEnum.LTL.getValue().equals(logisticsCarrier)){
                            checkLabelNum(0,tempOrder.getProductQuantity(),sysOssVo.getUrl());
                        }
                    }catch (ShippingValidationException e){
                        throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelSkuNotMatch"));
                    }

                    OrderAttachment newOrderAttachment = new OrderAttachment();
                    newOrderAttachment.setOssId(sysOssVo.getOssId());
                    newOrderAttachment.setOrderNo(orderNo);
                    newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
                    newOrderAttachment.setAttachmentOriginalName(originalName);
                    newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
                    newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
                    newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
                    newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);
                    attachmentList.add(newOrderAttachment);
                    orderNos.add(orderNo);
                    tempOrder.setShippingLabelOssId(sysOssVo.getOssId());
                    tempOrder.setShippingLabelFileName(originalName);
                    orderImportTemps.add(tempOrder);
                    batchUploadTips.append(StrUtil.format(rowSuccessTemplate, MessageUtils.message("zsmall.orders.shippingLabelMatch"), fileName));
                }

            } catch (RStatusCodeException e) {
                batchUploadTips.append(StrUtil.format(rowErrorTemplate, e.getMessage(), fileName));
            }
        }
        if(CollUtil.isNotEmpty(orderNos)){
            iOrderAttachmentService.deleteByOrderNosAndAttachmentType(orderNos,OrderAttachmentTypeEnum.ShippingLabel);
            iOrderAttachmentService.saveBatch(attachmentList);
            iOrderImportTempService.updateBatchById(orderImportTemps);
        }
        return R.okHtml(batchUploadTips.toString());
    }

    /**
     * 删除临时订单
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> deleteTempOrder(TempOrderUpdateBo bo) {
        String tempOrderNo = bo.getTempOrderNo();
        OrderImportTemp tempOrder = iOrderImportTempService.queryByTempOrderNoAndTempState(tempOrderNo, OrderTempState.Temporary);
        if (tempOrder == null) {
            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
        }
        iOrderImportTempService.removeById(tempOrder);
        return R.ok();
    }

    /**
     * 取消导入
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> cancelImport(OrderImportRecordBo bo) {
        String recordNo = bo.getRecordNo();
        OrderImportRecord orderImportRecord = iOrderImportRecordService.queryByRecordNoAndState(recordNo, ImportStateEnum.Pending);
        if (orderImportRecord == null) {
            return R.fail(ZSMallStatusCodeEnum.IMPORT_RECORD_NOT_FOUND);
        }

        orderImportRecord.setImportState(ImportStateEnum.Cancel);
        iOrderImportRecordService.updateById(orderImportRecord);

        Long recordId = orderImportRecord.getId();
        List<OrderImportTemp> tempList = iOrderImportTempService.queryByRecordIdAndTempState(recordId, OrderTempState.Temporary);
        if (CollUtil.isNotEmpty(tempList)) {
            tempList.forEach(temp -> temp.setTempState(OrderTempState.Canceled));
            iOrderImportTempService.updateBatchById(tempList);
        }
        return R.ok();
    }

    @Override
    public R<Void> batchUploadShippingLabelForChannelOrder(String orderNo, List<MultipartFile> fileList) {


        StrBuilder batchUploadTips = StrBuilder.create(MessageUtils.message("zsmall.orders.batchUploadShippingLabelTips"));

        String rowSuccessTemplate = "<p><span style='color: green'>[{}]{}</span></p>";
        String rowErrorTemplate = "<p><span style='color: red'>[{}]{}</span></p>";
        for (MultipartFile file : fileList) {
            String fileName = file.getOriginalFilename();
            if (StrUtil.contains(fileName, ".PDF")) {
                fileName = StrUtil.replace(fileName, ".PDF", ".pdf");
            }

            try {
                Orders order = iOrdersService.getByOrderNo(orderNo);
                OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);

                LogisticsTypeEnum logisticsType = order.getLogisticsType();
                String logisticsAccount = logisticsInfo.getLogisticsAccount();


                // 填写了第三方账号，无需上传快递标签
                if (StrUtil.isNotBlank(logisticsAccount)) {
                    throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelNotMatch"));
                }

                // 不是自提订单直接跳过
                if (!LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    throw new RStatusCodeException(MessageUtils.message("zsmall.orders.shippingLabelNotMatch"));
                }

                OSSUploadEvent ossUploadEvent = new OSSUploadEvent(file);
                SpringUtils.context().publishEvent(ossUploadEvent);
                SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
                String originalName = sysOssVo.getOriginalName();
                String suffix = FileUtil.getSuffix(originalName);

                OrderAttachment newOrderAttachment = new OrderAttachment();
                newOrderAttachment.setOssId(sysOssVo.getOssId());
                newOrderAttachment.setOrderNo(orderNo);
                newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
                newOrderAttachment.setAttachmentOriginalName(originalName);
                newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
                newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
                newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
                newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);

                iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
                iOrderAttachmentService.save(newOrderAttachment);

                batchUploadTips.append(StrUtil.format(rowSuccessTemplate, MessageUtils.message("zsmall.orders.shippingLabelMatch"), fileName));
            } catch (RStatusCodeException e) {
                batchUploadTips.append(StrUtil.format(rowErrorTemplate, e.getMessage(), fileName));
            }
        }
        return R.okHtml(batchUploadTips.toString());
    }

    @Override
    public R<Void> updateForChannelOrder(String orderNo, String updateType) {

//        if (StrUtil.isBlank(orderNo)) {
//            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
//        }
//
//        OrderAttachment orderAttachment = iOrderAttachmentService.getByOrderNo(orderNo);
//        if (orderAttachment == null) {
//            return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_NOT_FOUND_ERROR);
//        }
//
//
//
//        /**
//         * 更新类型：
//         * AddressInfo-地址信息，
//         * LogisticsInfo-物流信息，
//         * ChannelInfo-渠道信息，
//         * UploadShippingLabel-上传快递标签，
//         * DeleteShippingLabel-删除快递标签，
//         * UploadOrderAttachment-上传订单附件
//         * DeleteOrderAttachment-删除订单附件
//         */
//        if (StrUtil.equals(updateType, "AddressInfo")) {
//            TempOrderUpdateBo.AddressInfo addressInfo = bo.getAddressInfo();
//            String recipientName = addressInfo.getRecipientName();
//            String phoneNumber = addressInfo.getPhoneNumber();
//            String address1 = addressInfo.getAddress1();
//            String address2 = addressInfo.getAddress2();
//            String city = addressInfo.getCity();
//            Long countryId = addressInfo.getCountryId();
//            Long stateId = addressInfo.getStateId();
//            String stateCode = addressInfo.getState();
//
//            String zipCode = addressInfo.getZipCode();
//
//            WorldLocationVo country = iWorldLocationService.queryById(countryId);
//            String countryCode = country.getLocationCode();
//
//            if (stateId != null) {
//                WorldLocationVo state = iWorldLocationService.queryById(stateId);
//                stateCode = state.getLocationCode();
//            }
//
//            String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
//            // 支持的国家
//            List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
//            // 支持的州
//            List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");
//
//            if (StrUtil.isBlank(recipientName)) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_RECIPIENT_NAME_BLANK_ERROR);
//            }
//            if (StrUtil.isBlank(phoneNumber)) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_BLANK_ERROR);
//            }
//            if (StrUtil.isBlank(address1)) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ADDRESS1_BLANK_ERROR);
//            }
//            if (StrUtil.isBlank(city)) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CITY_BLANK_ERROR);
//            }
//
//            // 校验手机号
//            if (StrUtil.isNotBlank(phoneNumber) && StrUtil.length(phoneNumber) > 80) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PHONE_ERROR);
//            }
//
//            // 校验国家
//            if (StrUtil.equals(countryCode, "US")) {
//                if (ObjectUtil.equals(countryId, 0L)) {
//                    return R.fail(ZSMallStatusCodeEnum.UNSUPPORTED_COUNTRIES);
//                }
//
//                if (StrUtil.isBlank(zipCode)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_BLANK_ERROR);
//                }
//
//                // 仅US的才校验州
//                if (!SUPPORT_STATE.contains(stateCode)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STATE_NOT_EXIST);
//                }
//                // 仅US的才校验邮编
//                if (!RegexUtil.matchUSPostalCode(zipCode)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_NOT_EXIST);
//                }
//            } else if (!SUPPORT_COUNTRY.contains(countryCode)) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_COUNTRY_NOT_EXIST);
//            }
//
//            tempOrder.setRecipientName(recipientName);
//            tempOrder.setPhoneNumber(phoneNumber);
//            tempOrder.setAddress1(address1);
//            tempOrder.setAddress2(address2);
//            tempOrder.setCity(city);
//            tempOrder.setStateCode(stateCode);
//            tempOrder.setCountryCode(countryCode);
//            tempOrder.setZipCode(zipCode);
//        } else if (StrUtil.equals(updateType, "LogisticsInfo")) {
//            TempOrderUpdateBo.LogisticsInfo logisticsInfo = bo.getLogisticsInfo();
//            Boolean thirdBilling = logisticsInfo.getLogisticsThirdBilling();
//            String logisticsCarrier = logisticsInfo.getLogisticsCarrier();
//            String logisticsServiceName = logisticsInfo.getLogisticsServiceName();
//            String logisticsAccount = logisticsInfo.getLogisticsAccount();
//            String logisticsAccountZipCode = logisticsInfo.getLogisticsAccountZipCode();
//            String logisticsType = logisticsInfo.getLogisticsType();
//            String warehouseCode = logisticsInfo.getWarehouseCode();
//            Boolean isMarketplace = logisticsInfo.getIsMarketplace();
//            List<String> logisticsTrackingNo = logisticsInfo.getLogisticsTrackingNo();
//            String productSkuCode = logisticsInfo.getProductSkuCode();
//
//            ProductSku productSku;
//            if (StrUtil.isNotBlank(warehouseCode)) {
//                productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseCode);
//                if (productSku == null) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE.args(productSkuCode, warehouseCode));
//                }
//            } else {
//                productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
//            }
//
//            // 是否第三方物流
//            if (thirdBilling) {
//                // 是否填写第三方物流账户
//                if (StrUtil.isBlank(logisticsAccount)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR);
//                }
//
//                // 是否填写第三方物流账户邮编
//                if (StrUtil.isBlank(logisticsAccountZipCode)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR);
//                }
//
//                // 是否填写承运商
//                if (StrUtil.isBlank(logisticsCarrier)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
//                }
//
//                // 仓库是否支持第三方发货
//                if (StrUtil.isNotBlank(logisticsAccount)) {
//                    //仓库是否支持第三方发货
//                    if (StrUtil.isNotBlank(warehouseCode)) {
//                        if (productSku != null) {
//                            boolean support = iWarehouseService.isSupportThirdCarrier(warehouseCode);
//                            if (!support) {
//                                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_WAREHOUSE_NOT_SUPPORT_3RDBILLING_ERROR);
//                            }
//                        }
//                    }
//                }
//            } else {
//                // 是否填写承运商
//                if (StrUtil.isBlank(logisticsCarrier)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_CARRIER_BLANK_ERROR);
//                }
//
//                // 非第三方物流时，是否填写了物流单号
//                if (CollUtil.isEmpty(logisticsTrackingNo)) {
//                    return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_TRACKING_NO_BLANK_ERROR);
//                }
//            }
//
//            if (Boolean.TRUE.equals(isMarketplace) && thirdBilling) {
//                tempOrder.setShippingLabelOssId(null);
//                tempOrder.setShippingLabelFileName(null);
//
//                logisticsTrackingNo = null;
//                OrderAttachment newOrderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//                if (newOrderAttachment != null) {
//                    iOrderAttachmentService.removeById(newOrderAttachment);
//                }
//            }
//
//            tempOrder.setTempOrderNo(tempOrderNo);
//            tempOrder.setLogisticsThirdBilling(thirdBilling);
//            tempOrder.setLogisticsCarrier(logisticsCarrier);
//            tempOrder.setLogisticsServiceName(logisticsServiceName);
//            tempOrder.setLogisticsAccount(logisticsAccount);
//            tempOrder.setLogisticsAccountZipCode(logisticsAccountZipCode);
//
//            if (!thirdBilling) {
//                tempOrder.setLogisticsAccount(null);
//                tempOrder.setLogisticsAccountZipCode(null);
//            }
//
//            tempOrder.setLogisticsType(LogisticsTypeEnum.valueOf(logisticsType));
//            tempOrder.setWarehouseSystemCode(warehouseCode);
//            if (CollUtil.isNotEmpty(logisticsTrackingNo)) {
//                tempOrder.setLogisticsTrackingNo(JSONUtil.parseArray(logisticsTrackingNo));
//            } else {
//                tempOrder.setLogisticsTrackingNo(null);
//            }
//        } else if (StrUtil.equals(updateType, "ChannelInfo")) {
//            TempOrderUpdateBo.ChannelInfo channelInfo = bo.getChannelInfo();
//            String channelOrderNo = channelInfo.getStoreOrderId();
//            // 查询此账户所有订单判断是否有重复的，排除Canceled的
//            boolean orderExists = iOrdersService.existsChannelOrderNo(channelOrderNo, OrderStateType.Canceled);
//            // 查询导入缓存表
//            boolean tempOrderExists = iOrderImportTempService.existsChannelOrderNoExcludeSelf(channelOrderNo, tempOrderId);
//            if (orderExists || tempOrderExists) {
//                return R.fail(ZSMallStatusCodeEnum.TEMP_ORDER_STORE_ORDER_ID_REPEAT);
//            } else {
//                tempOrder.setStoreName(StrUtil.emptyToNull(channelInfo.getStoreName()));
//                tempOrder.setStoreOrderId(StrUtil.emptyToNull(channelOrderNo));
//            }
//        } else if (StrUtil.equals(updateType, "UploadShippingLabel")) {
//            MultipartFile multipartFile = bo.getMultipartFile();
//            if (multipartFile == null) {
//                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
//            }
//
//            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
//            SpringUtils.context().publishEvent(ossUploadEvent);
//            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
//            String originalName = sysOssVo.getOriginalName();
//            String suffix = FileUtil.getSuffix(originalName);
//
//            OrderAttachment newOrderAttachment = new OrderAttachment();
//            newOrderAttachment.setOssId(sysOssVo.getOssId());
//            newOrderAttachment.setOrderNo(orderNo);
//            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
//            newOrderAttachment.setAttachmentOriginalName(originalName);
//            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
//            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
//            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
//            newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);
//
//            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//            iOrderAttachmentService.save(newOrderAttachment);
//
//            tempOrder.setShippingLabelOssId(sysOssVo.getOssId());
//            tempOrder.setShippingLabelFileName(originalName);
//        } else if (StrUtil.equals(updateType, "DeleteShippingLabel")) {
//            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//            tempOrder.setShippingLabelOssId(null);
//            tempOrder.setShippingLabelFileName(null);
//        } else if (StrUtil.equals(updateType, "UploadOrderAttachment")) {
//            MultipartFile multipartFile = bo.getMultipartFile();
//            if (multipartFile == null) {
//                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
//            }
//
//            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
//            SpringUtils.context().publishEvent(ossUploadEvent);
//            SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
//            String originalName = sysOssVo.getOriginalName();
//            String suffix = FileUtil.getSuffix(originalName);
//
//            OrderAttachment newOrderAttachment = new OrderAttachment();
//            newOrderAttachment.setOssId(sysOssVo.getOssId());
//            newOrderAttachment.setOrderNo(orderNo);
//            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
//            newOrderAttachment.setAttachmentOriginalName(originalName);
//            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
//            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
//            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
//            newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.Zip);
//
//            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
//            iOrderAttachmentService.save(newOrderAttachment);
//        } else if (StrUtil.equals(updateType, "DeleteOrderAttachment")) {
//            iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
//        }
//
//        iOrderImportTempService.updateById(tempOrder);
        return R.ok();
    }


}
