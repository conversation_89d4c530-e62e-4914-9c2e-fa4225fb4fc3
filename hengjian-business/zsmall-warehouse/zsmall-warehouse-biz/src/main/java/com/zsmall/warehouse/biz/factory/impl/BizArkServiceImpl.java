package com.zsmall.warehouse.biz.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSObtainEvent;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.service.ISysApiService;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.hengjian.stream.mqProducer.producer.RedisProducer;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.iservice.IProductActivityStockService;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.orderLogistics.ShipServiceLevelEnum;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.UnitConverter;
import com.zsmall.extend.wms.exception.ThebizarkException;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.extend.wms.model.order.*;
import com.zsmall.extend.wms.model.stock.OutStock;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.event.SetOrderFulfillmentProgressEvent;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import com.zsmall.product.entity.iservice.IProductSkuDetailService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.TenantSite;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackPackagesItemsVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackPackagesVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackVo;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.ITenantSiteService;
import com.zsmall.warehouse.biz.factory.ThirdWarehouseFactory;
import com.zsmall.warehouse.biz.factory.ThirdWarehouseService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 第三方仓储库存-恒健仓库实现
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BizArkServiceImpl implements ThirdWarehouseService {
    private final SysOssMapper ossMapper;
    private final RedisProducer redisProducer;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IWarehouseService iWarehouseService;
    private final IProductActivityStockService iProductActivityStockService;
    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final IWarehouseService warehouseService;
    private final IProductSkuPriceService iProductSkuPriceService;

    private final BusinessParameterService businessParameterService;

    private final RabbitTemplate rabbitTemplate;

    private final ITenantSalesChannelService iTenantSalesChannelService;

    private final ISysApiService sysApiService;
    private final ITenantSiteService iTenantSiteService;
    private final IProductSkuDetailService iProductSkuDetailService;
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private ISysTenantService sysTenantService;


    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdWarehouseFactory.register(WarehouseTypeEnum.BizArk, this);
    }


    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }

    /**
     * 根据供应商的租户id获取店铺flag
     *
     * @param supplierId
     * @return
     */
    private SysApi getChannelFlagByTenantId(String supplierId){
        if(StringUtils.isNotEmpty(supplierId)){
            SysApi sysApi = sysApiService.getSysApiByTenantId(supplierId);
            if(null != sysApi){
                return sysApi;
            }
        }
        return null;
    }

    /**
     * 查询第三方仓储系统库存
     *
     * @param productSkuCode 商品SKU编号
     */
    @Override
    public void queryStockByProductSku(String productSkuCode) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        try {
            String supplierId = productSku.getTenantId();
            if (StrUtil.isBlank(supplierId)) {
                throw new ThebizarkException(1001, "供货商租户编号为空");
            }
            log.info("恒健仓库 - supplierId = {} 商品SKU查询库存 = {}", supplierId, productSkuCode);

            if (productSku != null) {
                String erpSku = productSku.getSku().replace(" ", "");
//                String erpSku = productSku.getErpSku();
                List<String> erpSkuList;
                if (StrUtil.isBlank(erpSku)) {
                    erpSkuList = CollUtil.newArrayList(productSku.getSku());
                } else {
                    erpSkuList = StrUtil.split(erpSku, ";");
                }
                ThebizarkDelegate delegate = initDelegate(supplierId);
                List<OutStock> outStockList = null;

                log.info("恒健仓库【查询库存】 supplierId = {} erpSkuList = {}", supplierId, JSONUtil.toJsonStr(erpSkuList));
                Result<List<OutStock>> result = delegate.stockApi().getInventory(erpSkuList);
                log.info("恒健仓库【查询库存】 查询库存结果 = {}", JSONUtil.toJsonStr(result));
                if (result.getStatusCode() != null && result.getStatusCode() == 200) {
                    outStockList = result.getData();
                }

                if (CollUtil.isNotEmpty(outStockList)) {
                    // 根据恒健仓库的文档筛选出可售库存
                    outStockList = outStockList.stream().filter(outStock -> outStock.getInventoryType() == 0)
                                               .collect(Collectors.toList());
                    BigDecimal totalInventory = BigDecimal.ZERO;
                    BigDecimal stockQuantity = BigDecimal.ZERO;
                    // 后续 产品梳理做planB
                    String code = "EFCA";
                    Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.queryByWarehouseType(supplierId, WarehouseTypeEnum.BizArk, code));
                    if (ObjectUtil.isEmpty(warehouse) || ObjectUtil.isEmpty(warehouse.getWarehouseSystemCode())) {
                        throw new StockException(ZSMallStatusCodeEnum.STOCK_STATUS_UNKNOWN);
                    }
                    for (OutStock outStock : outStockList) {
//                        String warehouseCode = outStock.getWarehouseCode();
                        Integer inventoryAvailable = outStock.getInventoryAvailable();

//                        Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.queryByWarehouseType(supplierId, WarehouseTypeEnum.BizArk, warehouseCode));
                        if (warehouse != null) {
                            // 查询活动库存占用
//                            Integer stockOccupation = iProductActivityStockService.queryActivityStockOccupation(productSkuCode, warehouse.getWarehouseSystemCode());
//                            log.info("同步恒健仓库库存 Item No. {} 原可用数量 = {} 活动库存占用数量 = {}", productSkuCode, inventoryAvailable, stockOccupation);
                            // 恒健仓库库存可用数量需要减去活动占用的数量
//                            BigDecimal newAvailable = BigDecimal.ZERO;
//                            if (NumberUtil.compare(stockOccupation, 0) > 0) {
//                                newAvailable = NumberUtil.sub(inventoryAvailable, stockOccupation);
//                                // 若相减后小于零，则强制赋值为0
//                                if (NumberUtil.isLess(newAvailable, BigDecimal.ZERO)) {
//                                    newAvailable = BigDecimal.ZERO;
//                                }
//                                inventoryAvailable = newAvailable.intValue();
//                            }
                            totalInventory = totalInventory.add(BigDecimal.valueOf(inventoryAvailable));


                        }
//                        iProductSkuStockService.updateByProductSkuCodeAndWarehouse(productSkuCode, warehouseCode, inventoryAvailable);
                    }
                    // 只减去一次库存
                    Integer stockOccupation = iProductActivityStockService.queryActivityStockOccupation(productSkuCode, warehouse.getWarehouseSystemCode());
                    log.info("同步恒健仓库库存 Item No. {} 原可用数量 = {} 活动库存占用数量 = {}", productSkuCode, totalInventory, stockOccupation);

                    if (totalInventory.subtract(BigDecimal.valueOf(stockOccupation)).compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal inventory = totalInventory.multiply(BigDecimal.valueOf(0.7))
                                                             .subtract(BigDecimal.valueOf(500));
                        if (inventory.compareTo(BigDecimal.valueOf(0)) > 0) {
                            stockQuantity = inventory.setScale(0, RoundingMode.UP);
                        }
                    }
                    log.info("同步恒健仓库库存 Item No. {} 新可用数量 = {}", productSkuCode, stockQuantity);
                    iProductSkuStockService.updateByProductSkuCodeAndWarehouse(productSkuCode, code, stockQuantity.intValue());

                }
            }
        } catch (Exception e) {
            log.error("恒健仓库【查询库存】 查询库存发生异常 {}", e.getMessage(), e);
        }

    }

    /**
     * 功能描述：产品上传 es入口
     *
     * @param product 产品
     * <AUTHOR>
     * @date 2024/03/20
     */
    public void productUpload(Product product) {
        if (product != null) {
            productUpload(CollUtil.newArrayList(product));
        }
    }

    public void productUpload(List<Product> productList) {
        if (CollUtil.isNotEmpty(productList)) {
            String[] productCodes = productList.stream().map(Product::getProductCode).toArray(String[]::new);
            productUpload(productCodes);
        }
    }

    public void productUpload(String... productCodes) {
        if (ArrayUtil.isNotEmpty(productCodes)) {
            Date date = new Date();
            MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));

            Map<String, Object> data = new HashMap<>();
            data.put("productCodes", List.of(productCodes));

            messageDto.setMsgSource("Es Product Push");
            messageDto.setData(data);
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, JSON.toJSONString(messageDto));
           // redisProducer.esProductPushProducer(messageDto);
        }
    }
//    支持多附件 多面单
//    /**
//     * 根据发货记录创建发货单
//     *
//     * @param shippingNo 发货单编号
//     */
//    @Override
//    public void createOrderByShippingRecord(String shippingNo) {
//        log.info("恒健仓库【根据发货记录创建发货单】 shippingNo = {}", shippingNo);
//        OrderItemShippingRecord shippingRecord = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNo);
//        log.info("恒健仓库【根据发货记录创建发货单】 = {}", JSONUtil.toJsonStr(shippingRecord));
//
//        String orderNo = shippingRecord.getOrderNo();
//        String orderItemNo = shippingRecord.getOrderItemNo();
//        ShippingOrderStateEnum shippingOrderState = ShippingOrderStateEnum.Created;
//
//        Orders orderData = iOrdersService.getByOrderNo(orderNo);
//        String channelOrderNo = orderData.getChannelOrderNo();
//        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(orderData.getTenantId());
//        List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderData.getChannelOrderNo()));
////        Boolean isHengjian = true;
//        try {
//            String supplierTenantId = shippingRecord.getSupplierTenantId();
//            String warehouseCode = shippingRecord.getWarehouseCode();
//            OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
////            if(null != orderItem && null != orderItem.getSupplierTenantId() && !orderItem.getSupplierTenantId().equals("SJN1857")){
////                isHengjian = false;
////                return;
////            }
//            ProductSkuPrice productSkuPrice = iProductSkuPriceService.getOne(new LambdaQueryWrapper<ProductSkuPrice>().eq(ProductSkuPrice::getProductSkuCode, orderItem.getProductSkuCode())
//                                                                                                                      .last("limit 1"));
//
//            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
//
//            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();
//
//            OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
//            String countryCode = addressInfo.getCountryCode();
//            String stateCode = addressInfo.getStateCode();
//            String city = addressInfo.getCity();
//            String recipient = addressInfo.getRecipient();
//            String phoneNumber = addressInfo.getPhoneNumber();
//            String address1 = addressInfo.getAddress1();
//            String address2 = addressInfo.getAddress2();
//
//            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
//            String logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
//            String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
//            String logisticsZipCode = orderLogisticsInfo.getLogisticsZipCode();
//
//            InCreateSaleOrder inCreateSaleOrder = new InCreateSaleOrder();
//            inCreateSaleOrder.setOrderNo(orderNo);
//            //仓库暂时置空
//            //inCreateSaleOrder.setWarehouseCode(warehouseCode);
//            inCreateSaleOrder.setThirdPartyAccount(logisticsAccount);
//            // 如果渠道单存在多个sku,在分销系统内会拆分成多个订单,此时,需要为渠道订单号添加后缀
//            // A-Z 26个字母 随机生成一个字母
//            if (ChannelTypeEnum.TikTok.equals(orderData.getChannelType())) {
//                if (orders.size() > 1) {
//                    // 将orderNo后四位截取
//                    String lastFourChars = null;
//                    if (orderNo.length() >= 4) {
//                        // 使用substring方法截取最后四位
//                        lastFourChars = orderNo.substring(orderNo.length() - 4);
//                    }
//                    channelOrderNo = channelOrderNo + "-" + lastFourChars;
//                    inCreateSaleOrder.setPoNumber(channelOrderNo);
//                } else {
//                    inCreateSaleOrder.setPoNumber(channelOrderNo);
//                }
//            } else {
//                inCreateSaleOrder.setPoNumber(orderData.getChannelOrderNo());
//            }
//            //inCreateSaleOrder.setPoNumber(orderNo);
//            String channelFlagByTenantId = getChannelFlagByTenantId(supplierTenantId);
//            if(StringUtils.isNotEmpty(channelFlagByTenantId)){
//                inCreateSaleOrder.setChannelAccount(channelFlagByTenantId);
//            }else {
//                inCreateSaleOrder.setChannelAccount(sysTenantVo.getChannelFlag());
//            }
//
//            inCreateSaleOrder.setShipToCountry(countryCode);
//            inCreateSaleOrder.setShipToState(stateCode);
//            inCreateSaleOrder.setShipToCity(city);
//
//            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//
//                logisticsZipCode = logisticsZipCode.replace("*", "0");
//            }
//            inCreateSaleOrder.setShipToPostal(logisticsZipCode);
//            inCreateSaleOrder.setShipToAddress1(address1);
//            inCreateSaleOrder.setShipToAddress2(address2);
//            inCreateSaleOrder.setShipToContact(recipient);
//            inCreateSaleOrder.setShipToTelephone(phoneNumber);
//            inCreateSaleOrder.setShipToMobile(phoneNumber);
//
//            InProductItems inProductItems = new InProductItems();
//            inProductItems.setSku(orderItemProductSku.getSku());
//            //inProductItems.setSku(orderItemProductSku.getErpSku());
//            inProductItems.setQuantity(orderItem.getTotalQuantity());
//            inProductItems.setIsInsure(0);
//            inProductItems.setIsSignature(0);
//            //分销订单暂时不存在多个sku的情况，只需提供实际支付金额即可 modified by Buddy @20240110
//            //inProductItems.setSkuAmount(orderItem.getPlatformActualUnitPrice().multiply(BigDecimal.valueOf(orderItem.getTotalQuantity())).doubleValue());
//            // 2.23 运营-产品确认 使用 应付金额代替原商品金额,此处的skuAmount应取 分销产品金额*订单中的包裹数量
//
//            // tiktok的订单推送需要拿数量
//            if (ChannelTypeEnum.TikTok.name().equals(orderItem.getChannelType().name())) {
//                // 判断自提还是一件代发
//                LogisticsTypeEnum type = orderData.getLogisticsType();
//                BigDecimal skuAmount = BigDecimal.ZERO;
//                if (LogisticsTypeEnum.PickUp.equals(type)) {
//                    skuAmount = orderData.getOriginalTotalPickUpPrice();
//                } else if (LogisticsTypeEnum.DropShipping.equals(type)) {
//                    skuAmount = orderData.getOriginalTotalDropShippingPrice();
//                }
//                inProductItems.setSkuAmount(skuAmount.doubleValue());
//            } else {
//                inProductItems.setSkuAmount(orderItem.getPlatformActualTotalAmount().doubleValue());
//            }
//
//            inCreateSaleOrder.setProductItems(CollUtil.newArrayList(inProductItems));
//
//            // 自提需要传单号和附件过去恒健仓库
//            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//
//                if (!StrUtil.containsAnyIgnoreCase(logisticsCompanyName, "UPS", "FEDEX", "USPS")) {
//                    JSONObject carrierSwitch = businessParameterService.getValueFromJSONObject(BusinessParameterType.HENGJIAN_CARRIER_SWITCH);
//                    String switchStr = carrierSwitch.getStr(logisticsCompanyName);
//                    if (switchStr != null) {
//                        logisticsCompanyName = switchStr;
//                    }
//                }
//
//                // 第三方账户为空，需要传物流单号和附件
//                if (StrUtil.isBlank(logisticsAccount)) {
//                    List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
//                    // 这里可能会有多个
//                    List<OrderAttachment> attachments = iOrderAttachmentService.queryListByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
////                    OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//                    HashMap<Long, List<String>> shippingLabels = new HashMap<>();
//                    if(CollUtil.isNotEmpty(attachments)){
//                        for (OrderAttachment attachment : attachments) {
//                            InputStream shippingLabel = OSSObtainEvent.obtainFileInputStream(attachment.getAttachmentShowUrl());
//                            List<String> strings = PDFUtil.splitPdfToBase64(shippingLabel, 1);
//                            shippingLabels.put(attachment.getId(),strings);
//                        }
//                    }else {
//                        throw new OrderPayException(OrderStatusCodeEnum.ORDER_SHIPPING_LABEL_NOT_EXISTS);
//                    }
////                    InputStream shippingLabel = OSSObtainEvent.obtainFileInputStream(orderAttachment.getAttachmentShowUrl());
//
////                    List<String> base64List = PDFUtil.splitPdfToBase64(shippingLabel, 1);
//                    List<InAttachInfo> inUploadAttachments = new ArrayList<>();
//                    // 根据物流单号数量拆分上传的PDF，要求物流单号数量与PDF页数一致
//                    if (CollectionUtils.isNotEmpty(trackingList)) {
//                        for (int i = 0; i < trackingList.size(); i++) {
//                            OrderItemTrackingRecord tracking = trackingList.get(i);
////                            从item_tracking_record表中获取物流单号
//                            String logisticsTrackingNo = tracking.getLogisticsTrackingNo();
//
//                            String base64;
////                            if (CollUtil.size(trackingList) == CollUtil.size(base64List)) {
////                                base64 = base64List.get(i);
////                            } else if (CollUtil.size(base64List) == 1) {
////                                base64 = base64List.get(0);
////                            } else {
////                                // x 测试完成后放开
////                                throw new OrderPayException(OrderStatusCodeEnum.INCONSISTENT_SHIPPING_LABEL_AND_TRACKING_NO);
////                            }
//                            for (Map.Entry<Long, List<String>> entry : shippingLabels.entrySet()) {
//                                List<String> base64List = entry.getValue();
//                                InAttachInfo inAttachInfo = new InAttachInfo();
//                                inAttachInfo.setName(logisticsTrackingNo);
//                                if("LTL".equals(logisticsCompanyName)){
//                                    inAttachInfo.setType(2);
//                                }else{
//                                    inAttachInfo.setType(0);
//                                }
//                                base64 = base64List.get(0);
//                                // x 测试完成后放开
//                                inAttachInfo.setFile(base64);
//                                inUploadAttachments.add(inAttachInfo);
//
//                            }
//
//                        }
//                    }
//                    // 如果tracking   logisticsTrackingNo
//
//                    if (ChannelTypeEnum.TikTok.equals(orderItem.getChannelType()) && CollUtil.isEmpty(inUploadAttachments)) {
//                        log.info("恒健仓库【创建发货单】附件补偿");
//                        // 从oss拿还是从 附件拿
//                        SysOss sysOss = ossMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, orderData.getLineOrderItemId()));
//                        String logisticsTrackingNo = sysOss.getBusinessNumber();
//
//                        String base64;
//                        for (Map.Entry<Long, List<String>> entry : shippingLabels.entrySet()) {
//                            List<String> base64List = entry.getValue();
//                            if (CollUtil.size(base64List) == 1) {
//                                base64 = base64List.get(0);
//                            } else {
//                                // x 测试完成后放开
//                                throw new OrderPayException(OrderStatusCodeEnum.INCONSISTENT_SHIPPING_LABEL_AND_TRACKING_NO);
//                            }
//                            InAttachInfo inAttachInfo = new InAttachInfo();
//                            inAttachInfo.setName(logisticsTrackingNo);
//                            if("LTL".equals(logisticsCompanyName)){
//                                inAttachInfo.setType(2);
//                            }else {
//                                inAttachInfo.setType(0);
//                            }
//
//                            // x 测试完成后放开
//                            inAttachInfo.setFile(base64);
//                            inUploadAttachments.add(inAttachInfo);
//
//                        }
//
//                    }
//
//
//                    if (CollUtil.isEmpty(inUploadAttachments)) {
//                        log.error("恒健仓库【创建发货单】 附件为空,订单号:{}", orderNo);
//                        throw new OrderPayException(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR);
//                    }
//                    inCreateSaleOrder.setAttachInfoItems(inUploadAttachments);
//
//                    log.info("CarrierCode value: {}", inCreateSaleOrder.getCarrierCode());
//                    //无附件 不传CarrierCode 有附件 传WLC
//                    if (!CollectionUtils.isEmpty(inUploadAttachments)) {
//                        //String bizArkCarrierCode = "WLC-" + logisticsCompanyName.toUpperCase();
//                        //inCreateSaleOrder.setCarrierCode(bizArkCarrierCode);
//                        inCreateSaleOrder.setCarrierCode("WLC-" + logisticsCompanyName.toUpperCase());
//
//                        log.info("CarrierCode value2: {}", inCreateSaleOrder.getCarrierCode());
//                    }
//
//                }
//            }
//            // 一件代发,需要确认承运商
//            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                if (StrUtil.containsAnyIgnoreCase(logisticsCompanyName, "UPS", "FEDEX", "USPS")) {
//                    inCreateSaleOrder.setCarrier(logisticsCompanyName);
//                }
//            }
//
//            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
//            delegate.orderApi().createSaleOrder(inCreateSaleOrder);
//            inCreateSaleOrder.setAttachInfoItems(null);
//            shippingRecord.setInCreateJson(JSONUtil.parseObj(inCreateSaleOrder));
//            shippingRecord.setShippingState(ShippingStateEnum.Unshipped);
//            orderData.setChannelReceiptStatus("Success");
//            orderData.setFulfillmentProgress(LogisticsProgress.UnDispatched);
//            orderData.setChannelOrderName(channelOrderNo);
//        } catch (OrderPayException e) {
//            log.info("恒健仓库【创建发货单】 发生异常（RStatusCodeException） {}", e.getMessage(), e);
//            shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
//            shippingOrderState = ShippingOrderStateEnum.CreateFailed;
//            shippingRecord.setShippingErrorCode(e.getMessage());
//            shippingRecord.setShippingErrorMessage(e.getLocaleMessage().toJSON());
//            orderData.setFulfillmentProgress(LogisticsProgress.Abnormal);
////            iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getOrderNo, orderNo)
////                                                                   .set(Orders::getFulfillmentProgress, LogisticsProgress.Abnormal));
//        } catch (Exception e) {
//            log.info("恒健仓库【创建发货单】 发生异常 {}", e.getMessage(), e);
//            shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
//            shippingOrderState = ShippingOrderStateEnum.CreateFailed;
//            shippingRecord.setShippingErrorCode(e.getMessage());
//            shippingRecord.setShippingErrorMessage(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR.toJSON());
//            orderData.setFulfillmentProgress(LogisticsProgress.Abnormal);
//        } finally {
//            TenantHelper.ignore(() -> iOrderItemShippingRecordService.updateById(shippingRecord));
//            iOrdersService.updateById(orderData);
////            if(isHengjian){
//            iOrderItemService.updateShippingOrderState(orderItemNo, shippingOrderState);
////            }
//        }
//
//    }
    /**
     * 根据发货记录创建发货单
     *
     * @param shippingNo 发货单编号
     */
    @Override
    public void createOrderByShippingRecord(String shippingNo) {
        // 如果是bol单走下面流程,非bol单合并推送,
        OrderItemShippingRecord shippingRecord = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNo);
        String orderNo = shippingRecord.getOrderNo();
        Orders order = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));

        OrderLogisticsInfo logisticsInfo = TenantHelper.ignore(() -> iOrderLogisticsInfoService.getByOrderNo(orderNo));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        String orderExtendId = order.getOrderExtendId();
        String logisticsCompanyName=null;
        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){


            if(ObjectUtil.isNotEmpty(logisticsInfo)){
                logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
            }
            // 自提-LTL-BOL size大与1 走合单推送 还得是open-api的 ;迭代放开至导单
            if (CarrierTypeEnum.LTL.getValue().equals(logisticsCompanyName) &&
                (OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource()) ||
                    OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())) ||
                        OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())) {

                BooleanResult result = getResult(orderExtendId);
                // 推送前校验
                if (result.ordersList.size() > 1 ){
                    if(!result.allPaid){
                        // 这里不用考虑睡眠
                        return;
                    }
                    String key = "shippingRecord"+":"+orderExtendId;
                    RLock lock = redissonClient.getLock(key);
                    boolean locked = lock.tryLock();
                    if(locked){
                        try {
                            // 先查订单是不是已经发货,需要在orders表加新字段
                            Orders deliverOrder = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));
                            if(!"Success".equals(deliverOrder.getChannelReceiptStatus())){
                                createOrderByShippingRecordMerge(shippingNo);
                            }
                        } finally {
                            // 释放锁
                            lock.unlock();
                        }
                    }
                    // 合并推送并且只推送一次,并且进行判断,是否两个订单都是Paid状态,推送完成后同时更新两个订单的发货状态.

                }else{
                    createOrderByShippingRecordNotMerge(shippingNo);
                }
            }else {
                createOrderByShippingRecordNotMerge(shippingNo);
            }
        }else {
            createOrderByShippingRecordNotMerge(shippingNo);
        }

    }

    @NotNull
    private BooleanResult getResult(String orderExtendId) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderExtendId, orderExtendId)
                                                                        .eq(Orders::getDelFlag,0);
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(wrapper));
        //ordersList内的元素的 orderState 状态都是Paid,返回true否则返回false
        boolean allPaid = ordersList.stream()
                                    .allMatch(item -> OrderStateType.Paid.equals(item.getOrderState()));
        BooleanResult result = new BooleanResult(ordersList, allPaid);
        return result;
    }

    private static class BooleanResult {
        public final List<Orders> ordersList;
        public final boolean allPaid;

        public BooleanResult(List<Orders> ordersList, boolean allPaid) {
            this.ordersList = ordersList;
            this.allPaid = allPaid;
        }
    }

    /**
     * 功能描述：通过发货记录合并创建订单
     *
     * @param shippingNo 装运编号
     * <AUTHOR>
     * @date 2024/11/05
     */
    private void createOrderByShippingRecordMerge(String shippingNo) {
        log.info("恒健仓库【根据发货记录创建发货单】 shippingNo = {}", shippingNo);
        OrderItemShippingRecord shippingRecord = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNo);
        log.info("恒健仓库【根据发货记录创建发货单】 = {}", JSONUtil.toJsonStr(shippingRecord));

        String orderNo = shippingRecord.getOrderNo();
        String orderItemNo = shippingRecord.getOrderItemNo();
        ShippingOrderStateEnum shippingOrderState = ShippingOrderStateEnum.Created;
        OrderItemProductSku one = iOrderItemProductSkuService.getOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, orderNo));
        // todo 如需测算放开
        Orders orderData = iOrdersService.getByOrderNo(orderNo);
        Integer isNeedLabeling = orderData.getIsNeedLabeling();
        Integer orderSource = orderData.getOrderSource();
        String orderExtendId = orderData.getOrderExtendId();
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderExtendId, orderExtendId)
                                                                             .eq(Orders::getDelFlag,0);
        List<Orders> orders = TenantHelper.ignore(() -> iOrdersService.list(wrapper));
        List<String> orderNumbers = orders.stream()
                                          .map(Orders::getOrderNo)
                                          .collect(Collectors.toList());
        LambdaQueryWrapper<OrderItemShippingRecord> lambdaQueryWrapper = new LambdaQueryWrapper<OrderItemShippingRecord>().in(OrderItemShippingRecord::getOrderNo, orderNumbers);

        List<OrderItemShippingRecord> shippingRecords = TenantHelper.ignore(()-> iOrderItemShippingRecordService.list(lambdaQueryWrapper));
        String channelOrderNo = orderData.getChannelOrderNo();

        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(orderData.getTenantId());
        InCreateSaleOrder inCreateSaleOrder = new InCreateSaleOrder();
        List<String> orderNos = new ArrayList<>();
        try {
            String supplierTenantId = shippingRecord.getSupplierTenantId();

            OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);

            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();

            OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
            String countryCode = addressInfo.getCountryCode();
            String stateCode = addressInfo.getStateCode();
            String city = addressInfo.getCity();
            String recipient = addressInfo.getRecipient();
            String phoneNumber = addressInfo.getPhoneNumber();
            String address1 = addressInfo.getAddress1();
            String address2 = addressInfo.getAddress2();
            String address3 = addressInfo.getAddress3();
            String companyName = addressInfo.getCompanyName();

            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
//            "Standard-48 Hours" ：标准（48小时发货）
////        "Same Day Ship Out" :（加急当日发货）不填为标准
            String shipServiceLevel = orderLogisticsInfo.getShipServiceLevel();

            String logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
            String logisticsZipCode = orderLogisticsInfo.getLogisticsZipCode();


            inCreateSaleOrder.setOrderNo(orderExtendId);
            inCreateSaleOrder.setCurrency(orderData.getCurrency());
            if(StrUtil.isNotEmpty(shipServiceLevel)){
                if(ShipServiceLevelEnum.STANDARD_48_HOURS.getMsg().equals(shipServiceLevel)||ShipServiceLevelEnum.SAME_DAY_SHIP_OUT.getMsg().equals(shipServiceLevel)){
                    inCreateSaleOrder.setShipServiceLevel(shipServiceLevel);
                }
            }
            // 转换为UTC时间
            if (ObjectUtil.isNotNull(orderData.getChannelOrderTime())){
                Instant instant = orderData.getChannelOrderTime().toInstant();
                // 使用Instant创建UTC时区的ZonedDateTime
                ZonedDateTime utcZonedDateTime = instant.atZone(ZoneId.of("UTC"));
                // 创建一个DateTimeFormatter来格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 将ZonedDateTime格式化为String
                String formattedDate = utcZonedDateTime.format(formatter);
                // 设置格式化后的日期字符串到inCreateSaleOrder对象的paidAt属性
                inCreateSaleOrder.setPaidAt(formattedDate);
            }
            //   orderData.getChannelOrderName()
            //仓库暂时置空
            //inCreateSaleOrder.setWarehouseCode(warehouseCode);
            inCreateSaleOrder.setThirdPartyAccount(logisticsAccount);
            inCreateSaleOrder.setPoNumber(orderData.getChannelOrderNo());
            //inCreateSaleOrder.setPoNumber(orderNo);
            SysApi sysApi = getChannelFlagByTenantId(supplierTenantId);
            if(ObjectUtil.isNotEmpty(sysApi)){
                if(null != sysApi.getIsPushErp() && sysApi.getIsPushErp() == 1){
                    log.info("租户 {} 不推送erp,无需创建发货单",supplierTenantId);
                    return;
                }
                inCreateSaleOrder.setChannelAccount(sysApi.getChannelFlag());
            }else {
                TenantSite tenantSite = iTenantSiteService.getByTenantIdAndCountryCode(orderData.getTenantId(), orderData.getCountryCode());
                if(ObjectUtil.isNotNull(tenantSite) && StringUtils.isNotEmpty(tenantSite.getChannelFlag())){
                    inCreateSaleOrder.setChannelAccount(tenantSite.getChannelFlag());
                }else {
                    log.error("根据租户id和国家code获取渠道标识为空，租户id: {}，国家code: {}",orderData.getTenantId(),orderData.getCountryCode());
                    throw new OrderPayException(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR);
                }
//                inCreateSaleOrder.setChannelAccount(sysTenantVo.getChannelFlag());
            }

            inCreateSaleOrder.setShipToCountry(countryCode);
            inCreateSaleOrder.setShipToState(stateCode);
            inCreateSaleOrder.setShipToCity(city);

            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {

                logisticsZipCode = logisticsZipCode.replace("*", "0");
            }
            inCreateSaleOrder.setShipToPostal(logisticsZipCode);
            inCreateSaleOrder.setShipToAddress1(address1);
            inCreateSaleOrder.setShipToAddress2(address2);
            inCreateSaleOrder.setShipToAddress3(address3);
            inCreateSaleOrder.setCompanyName(companyName);
            inCreateSaleOrder.setShipToContact(recipient);
            inCreateSaleOrder.setShipToTelephone(phoneNumber);
            inCreateSaleOrder.setShipToMobile(phoneNumber);
            List<InProductItems> productItems = new ArrayList<>();
            BigDecimal amount = new BigDecimal(0);
            // 这里会有多个

            for (Orders order : orders) {
                LambdaQueryWrapper<OrderItemProductSku> wrapper1 = new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, order.getOrderNo()).eq(OrderItemProductSku::getDelFlag,0).last("limit 1");
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getOne(wrapper1);
                InProductItems inProductItems = new InProductItems();
                inProductItems.setSku(orderItemProductSku.getSku());
                //inProductItems.setSku(orderItemProductSku.getErpSku());
                inProductItems.setQuantity(order.getTotalQuantity());
                inProductItems.setIsInsure(0);
                inProductItems.setIsSignature(0);
                //open-api订单存在多个sku的场景需要计算合计价格.
                orderNos.add(order.getOrderNo());

//                inProductItems.setSkuAmount(orderItem.getPlatformActualUnitPrice().multiply(BigDecimal.valueOf(orderItem.getTotalQuantity())).doubleValue());
                LogisticsTypeEnum type = orderData.getLogisticsType();
                BigDecimal skuAmount = BigDecimal.ZERO;
                if (LogisticsTypeEnum.PickUp.equals(type)) {
                    skuAmount = order.getOriginalTotalPickUpPrice();
                } else if (LogisticsTypeEnum.DropShipping.equals(type)) {
                    skuAmount = order.getOriginalTotalDropShippingPrice();
                }
                amount = amount.add(skuAmount);
                inProductItems.setSkuAmount(skuAmount.doubleValue());
                productItems.add(inProductItems);
            }
            inCreateSaleOrder.setAmount(amount.doubleValue());
            inCreateSaleOrder.setProductItems(productItems);

            // 自提需要传单号和附件过去恒健仓库
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                Warehouse warehouse = warehouseService.queryByWarehouseSystemCode(one.getWarehouseSystemCode());
                if (ObjectUtil.isNotNull(warehouse)){
                    inCreateSaleOrder.setWarehouseCode(warehouse.getWarehouseCode());
                }else {
                    throw new OrderPayException("仓库信息不存在"+one.getWarehouseSystemCode());
                }
                if (!StrUtil.containsAnyIgnoreCase(logisticsCompanyName, "UPS", "FEDEX", "USPS")) {
                    JSONObject carrierSwitch = businessParameterService.getValueFromJSONObject(BusinessParameterType.HENGJIAN_CARRIER_SWITCH);
                    String switchStr = carrierSwitch.getStr(logisticsCompanyName);
                    if (switchStr != null) {
                        logisticsCompanyName = switchStr;
                    }
                }

                // 第三方账户为空，需要传物流单号和附件
                if (StrUtil.isBlank(logisticsAccount)) {
                    List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                    // 拿出所有相关 然后塞list进去
                    List<OrderAttachment> orderAttachments = iOrderAttachmentService.getByOrderNos(Collections.singletonList(orderNo));

                    List<InAttachInfo> inUploadAttachments = new ArrayList<>();
                    OrderItemTrackingRecord record = trackingList.get(0);
                    for (OrderAttachment attachment : orderAttachments) {
                        InputStream shippingLabel = OSSObtainEvent.obtainFileInputStream(attachment.getAttachmentShowUrl());
                        if (shippingLabel == null) {
                            throw new OrderPayException(OrderStatusCodeEnum.ORDER_SHIPPING_LABEL_NOT_EXISTS);
                        }
//                        String base64 = PDFUtil.convertPdfToBase64(shippingLabel);
                        String logisticsTrackingNo = record.getLogisticsTrackingNo();
                        InAttachInfo inAttachInfo = new InAttachInfo();
                        if(ObjectUtil.isNotEmpty(logisticsTrackingNo)){
                            inAttachInfo.setName(record.getLogisticsTrackingNo());
                        }else {
                            inAttachInfo.setName(String.valueOf(attachment.getId()));
                        }

                        if(OrderAttachmentTypeEnum.ShippingLabel.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(OrderAttachmentTypeEnum.ShippingLabel.getCode());
                        }
                        if(OrderAttachmentTypeEnum.BOL.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(OrderAttachmentTypeEnum.BOL.getCode());
                        }
                        if(OrderAttachmentTypeEnum.CartonLabel.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(OrderAttachmentTypeEnum.CartonLabel.getCode());
                        }
                        if(OrderAttachmentTypeEnum.PalletLabel.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(OrderAttachmentTypeEnum.PalletLabel.getCode());
                        }
                        if(OrderAttachmentTypeEnum.ItemLabel.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(OrderAttachmentTypeEnum.ItemLabel.getCode());
                        }
                        if(OrderAttachmentTypeEnum.Other.equals(attachment.getAttachmentType())){
                            inAttachInfo.setType(3);
                        }
                        if(OrderAttachmentTypeEnum.Zip.equals(attachment.getAttachmentType())){
                            continue;
                        }
                        inAttachInfo.setUrl(attachment.getAttachmentShowUrl());
                        inUploadAttachments.add(inAttachInfo);
                    }

                    if (CollUtil.isEmpty(inUploadAttachments)) {
                        log.error("恒健仓库【创建发货单】 附件为空,订单号:{}", orderNo);
                        throw new OrderPayException(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR);
                    }
                    // 如果是自提,open-api接口-LTL,附件类型为2且inUploadAttachments内部没有附件,则需要补充附件
                    if(OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)&& "LTL".equals(logisticsCompanyName)){
                        // 如果inUploadAttachments的元素内没有type为2的元素,返回true
                        boolean isHaveBol = inUploadAttachments.stream()
                                                              .noneMatch(attachInfo -> Objects.equals(attachInfo.getType(), OrderAttachmentTypeEnum.BOL.getCode()));
                        if(isHaveBol){
                            log.info("恒健仓库【创建发货单】,open-api订单,空白BOL附件,临时空pdf补偿");
                            InAttachInfo inAttachInfo = new InAttachInfo();
                            inAttachInfo.setName("emptyBOL");
                            inAttachInfo.setType(OrderAttachmentTypeEnum.BOL.getCode());
                            inAttachInfo.setUrl("https://hengjian-distribution.oss-cn-hangzhou.aliyuncs.com/2025/01/09/02d9f4b015a24a2abfb3d5f205583315.pdf");
                            inUploadAttachments.add(inAttachInfo);
                        }

                    }
                    inCreateSaleOrder.setAttachInfoItems(inUploadAttachments);

                    log.info("CarrierCode value: {}", inCreateSaleOrder.getCarrierCode());
                    //无附件 不传CarrierCode 有附件 传WLC
//                    if (!CollectionUtils.isEmpty(inUploadAttachments)) {
//                        if (!CollectionUtils.isEmpty(inUploadAttachments)) {
//                            // open-api且不是LTL的订单且,is need Labeling=0,则推送至erp是FBASP;其余的如果是LTL则推送至erp是FBALTL
//
//                        }
//
//                        log.info("CarrierCode value2: {}", inCreateSaleOrder.getCarrierCode());
//                    }
                    carrierMethod(isNeedLabeling, orderSource, logisticsCompanyName, inCreateSaleOrder, orderItem);
                }
            }
            // 添加租户id和公司名称
            inCreateSaleOrder.setDistributionCustomId(orderData.getTenantId());
            if(null != sysTenantVo){
                inCreateSaleOrder.setDistributionCustomName(sysTenantVo.getCompanyName());
            }
            log.info("推送json"+JSONUtil.toJsonStr(inCreateSaleOrder));
            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            delegate.orderApi().createSaleOrder(inCreateSaleOrder);
            inCreateSaleOrder.setAttachInfoItems(null);

            for (OrderItemShippingRecord record : shippingRecords) {
                record.setInCreateJson(JSONUtil.parseObj(inCreateSaleOrder));
                record.setShippingState(ShippingStateEnum.Unshipped);
            }

            for (Orders order : orders) {
                order.setChannelReceiptStatus("Success");
                // 目前除了10以外所有的异常都在支付前,此处还原到0 应该不会有问题
                order.setExceptionCode(0);
                order.setFulfillmentProgress(LogisticsProgress.UnDispatched);
                order.setChannelOrderName(channelOrderNo);
            }

        } catch (OrderPayException e) {
            log.error("恒健仓库【创建发货单】 发生异常（RStatusCodeException）", e);
            for (OrderItemShippingRecord record : shippingRecords) {
                record.setShippingState(ShippingStateEnum.CreateFailed);
                String message = e.getMessage();
                if(StrUtil.isNotEmpty(message)){
                    record.setShippingErrorCode(message);
                }else {
                    record.setShippingErrorCode("创建发货单异常");
                }
                record.setShippingErrorMessage(e.getLocaleMessage().toJSON());
            }

            shippingOrderState = ShippingOrderStateEnum.CreateFailed;

            for (Orders order : orders) {
                // todo 记录异常原因
                order.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
                order.setFulfillmentProgress(LogisticsProgress.Abnormal);
            }


        } catch (Exception e) {
            log.error("恒健仓库【创建发货单】 发生异常 ", e);
            for (OrderItemShippingRecord record : shippingRecords) {
                record.setShippingState(ShippingStateEnum.CreateFailed);
                String message = e.getMessage();
                if(StrUtil.isNotEmpty(message)){
                    record.setShippingErrorCode(message);
                }else {
                    record.setShippingErrorCode("创建发货单异常");
                }

                record.setShippingErrorMessage(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR.toJSON());
            }

            shippingOrderState = ShippingOrderStateEnum.CreateFailed;

            for (Orders order : orders) {
                // todo 记录异常原因
                order.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
                order.setFulfillmentProgress(LogisticsProgress.Abnormal);
            }
        } finally {
            List<OrderItemShippingRecord> finalShippingRecords = shippingRecords;
            TenantHelper.ignore(() -> iOrderItemShippingRecordService.updateBatchById(finalShippingRecords));
            iOrdersService.updateBatchById(orders);
//            iOrderItemService.updateShippingOrderState(orderItemNo, shippingOrderState);
            iOrderItemService.updateShippingOrderStateByOrderNos(orderNos, shippingOrderState);
        }
    }

    private static void carrierMethod(Integer isNeedLabeling, Integer orderSource, String logisticsCompanyName,
                                  InCreateSaleOrder inCreateSaleOrder, OrderItem orderItem) {
        if(ObjectUtil.isNotEmpty(isNeedLabeling) && isNeedLabeling == 0 && OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource) && !"LTL".equals(logisticsCompanyName)){
            inCreateSaleOrder.setCarrierCode("FBASP");
        }
        if(ObjectUtil.isNotEmpty(isNeedLabeling) && isNeedLabeling == 1 && OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource) && !"LTL".equals(logisticsCompanyName)){
            nonLtlCarrierHandler(orderItem, logisticsCompanyName, inCreateSaleOrder);
        }
        if(ObjectUtil.isNotEmpty(isNeedLabeling) && isNeedLabeling == 0 && OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource) && "LTL".equals(logisticsCompanyName)){
            inCreateSaleOrder.setCarrierCode("FBALTL");
        }
        if(ObjectUtil.isNotEmpty(isNeedLabeling) && isNeedLabeling == 1 && OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource) && "LTL".equals(logisticsCompanyName)){
            inCreateSaleOrder.setCarrierCode("LTL");
        }
        if(ObjectUtil.isNotEmpty(isNeedLabeling) && isNeedLabeling == 1 && OrderSourceEnum.EXCEL_ORDER.getValue().equals(orderSource) && "LTL".equals(logisticsCompanyName)){
            inCreateSaleOrder.setCarrierCode("LTL");
        }
        if(CharSequenceUtil.isBlank(inCreateSaleOrder.getCarrierCode())){
            nonLtlCarrierHandler(orderItem, logisticsCompanyName, inCreateSaleOrder);
        }
    }

    private void createOrderByShippingRecordNotMerge(String shippingNo) {
        log.info("恒健仓库【根据发货记录创建发货单】 shippingNo = {}", shippingNo);
        OrderItemShippingRecord shippingRecord = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNo);
        log.info("恒健仓库【根据发货记录创建发货单】 = {}", JSONUtil.toJsonStr(shippingRecord));

        String orderNo = shippingRecord.getOrderNo();
        String orderItemNo = shippingRecord.getOrderItemNo();
        ShippingOrderStateEnum shippingOrderState = ShippingOrderStateEnum.Created;
        OrderItemProductSku one = iOrderItemProductSkuService.getOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, orderNo));
        // todo 如需测算放开
        Orders orderData = iOrdersService.getByOrderNo(orderNo);
        Integer isNeedLabeling = orderData.getIsNeedLabeling();
        Integer orderSource = orderData.getOrderSource();
        String channelOrderNo = orderData.getChannelOrderNo();
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(orderData.getTenantId());
        List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderData.getChannelOrderNo()));
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(orderData.getTenantId(), 1);
        String warehouseCode =null;
        InCreateSaleOrder inCreateSaleOrder = new InCreateSaleOrder();
//        Boolean isHengjian = true;
        try {
            String supplierTenantId = shippingRecord.getSupplierTenantId();


            OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
//            if(null != orderItem && null != orderItem.getSupplierTenantId() && !orderItem.getSupplierTenantId().equals("SJN1857")){
//                isHengjian = false;
//                return;
//            }

            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();

            OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
            String countryCode = addressInfo.getCountryCode();
            String stateCode = addressInfo.getStateCode();
            String city = addressInfo.getCity();
            String recipient = addressInfo.getRecipient();
            String phoneNumber = addressInfo.getPhoneNumber();
            String address1 = addressInfo.getAddress1();
            String address2 = addressInfo.getAddress2();
            String address3 = addressInfo.getAddress3();
            String companyName = addressInfo.getCompanyName();

            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
//            "Standard-48 Hours" ：标准（48小时发货）
////        "Same Day Ship Out" :（加急当日发货）不填为标准
            String shipServiceLevel = orderLogisticsInfo.getShipServiceLevel();

            String logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
            String logisticsZipCode = orderLogisticsInfo.getLogisticsZipCode();


            inCreateSaleOrder.setOrderNo(orderNo);
            if(StrUtil.isNotEmpty(shipServiceLevel)){
                if(ShipServiceLevelEnum.STANDARD_48_HOURS.getMsg().equals(shipServiceLevel)||ShipServiceLevelEnum.SAME_DAY_SHIP_OUT.getMsg().equals(shipServiceLevel)){
                    inCreateSaleOrder.setShipServiceLevel(shipServiceLevel);
                }
            }
            // 转换为UTC时间
            if (ObjectUtil.isNotNull(orderData.getChannelOrderTime())){
                Instant instant = orderData.getChannelOrderTime().toInstant();
                // 使用Instant创建UTC时区的ZonedDateTime
                ZonedDateTime utcZonedDateTime = instant.atZone(ZoneId.of("UTC"));
                // 创建一个DateTimeFormatter来格式化日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 将ZonedDateTime格式化为String
                String formattedDate = utcZonedDateTime.format(formatter);
                // 设置格式化后的日期字符串到inCreateSaleOrder对象的paidAt属性
                inCreateSaleOrder.setPaidAt(formattedDate);
            }
         //   orderData.getChannelOrderName()
            //仓库暂时置空
            //inCreateSaleOrder.setWarehouseCode(warehouseCode);
            inCreateSaleOrder.setThirdPartyAccount(logisticsAccount);
            inCreateSaleOrder.setCurrency(orderData.getCurrency());
            // 如果渠道单存在多个sku,在分销系统内会拆分成多个订单,此时,需要为渠道订单号添加后缀
            // A-Z 26个字母 随机生成一个字母
            if (ChannelTypeEnum.TikTok.equals(orderData.getChannelType())) {
                if (orders.size() > 1) {
                    // 将orderNo后四位截取
                    String lastFourChars = null;
                    if (orderNo.length() >= 4) {
                        // 使用substring方法截取最后四位
                        lastFourChars = orderNo.substring(orderNo.length() - 4);
                    }
                    channelOrderNo = channelOrderNo + "-" + lastFourChars;
                    inCreateSaleOrder.setPoNumber(channelOrderNo);
                } else {
                    inCreateSaleOrder.setPoNumber(channelOrderNo);
                }
            }else if (ChannelTypeEnum.Temu.equals(orderData.getChannelType())){
                channelOrderNo = orderData.getChannelOrderName();
                inCreateSaleOrder.setPoNumber(orderData.getChannelOrderName());
            }else {
                inCreateSaleOrder.setPoNumber(orderData.getChannelOrderNo());
            }
            //inCreateSaleOrder.setPoNumber(orderNo);
            SysApi sysApi = getChannelFlagByTenantId(supplierTenantId);
            if(ObjectUtil.isNotEmpty(sysApi)){
                if(null != sysApi.getIsPushErp() && sysApi.getIsPushErp() == 1){
                    log.info("租户 {} 不推送erp,无需创建发货单",supplierTenantId);
                    return;
                }
                inCreateSaleOrder.setChannelAccount(sysApi.getChannelFlag());
            } else {
                TenantSite tenantSite = iTenantSiteService.getByTenantIdAndCountryCode(orderData.getTenantId(), orderData.getCountryCode());
                if(ObjectUtil.isNotNull(tenantSite) && StringUtils.isNotEmpty(tenantSite.getChannelFlag())){
                    inCreateSaleOrder.setChannelAccount(tenantSite.getChannelFlag());
                }else {
                    log.error("根据租户id和国家code获取渠道标识为空，租户id: {}，国家code: {}",orderData.getTenantId(),orderData.getCountryCode());
                    throw new OrderPayException(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR);
                }
//                inCreateSaleOrder.setChannelAccount(sysTenantVo.getChannelFlag());
            }

            inCreateSaleOrder.setShipToCountry(countryCode);
            inCreateSaleOrder.setShipToState(stateCode);
            inCreateSaleOrder.setShipToCity(city);

            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {

                logisticsZipCode = logisticsZipCode.replace("*", "0");
            }
            inCreateSaleOrder.setShipToPostal(logisticsZipCode);
            inCreateSaleOrder.setShipToAddress1(address1);
            inCreateSaleOrder.setShipToAddress2(address2);
            inCreateSaleOrder.setShipToAddress3(address3);
            inCreateSaleOrder.setCompanyName(companyName);
            inCreateSaleOrder.setShipToContact(recipient);
            inCreateSaleOrder.setShipToTelephone(phoneNumber);
            inCreateSaleOrder.setShipToMobile(phoneNumber);

            InProductItems inProductItems = new InProductItems();
            inProductItems.setSku(orderItemProductSku.getSku());
            //inProductItems.setSku(orderItemProductSku.getErpSku());
            inProductItems.setQuantity(orderItem.getTotalQuantity());
            inProductItems.setIsInsure(0);
            inProductItems.setIsSignature(0);
            //分销订单暂时不存在多个sku的情况，只需提供实际支付金额即可 modified by Buddy @20240110
            //inProductItems.setSkuAmount(orderItem.getPlatformActualUnitPrice().multiply(BigDecimal.valueOf(orderItem.getTotalQuantity())).doubleValue());
            // 2.23 运营-产品确认 使用 应付金额代替原商品金额,此处的skuAmount应取 分销产品金额*订单中的包裹数量

            // tiktok的订单推送需要拿数量
//            if (ChannelTypeEnum.TikTok.name().equals(orderItem.getChannelType().name())) {
//                // 判断自提还是一件代发
//                LogisticsTypeEnum type = orderData.getLogisticsType();
//                BigDecimal skuAmount = BigDecimal.ZERO;
//                if (LogisticsTypeEnum.PickUp.equals(type)) {
//                    skuAmount = orderData.getOriginalTotalPickUpPrice();
//                } else if (LogisticsTypeEnum.DropShipping.equals(type)) {
//                    skuAmount = orderData.getOriginalTotalDropShippingPrice();
//                }
//                inProductItems.setSkuAmount(skuAmount.doubleValue());
//            } else {
//                inProductItems.setSkuAmount(orderItem.getPlatformActualTotalAmount().doubleValue());
//            }
            LogisticsTypeEnum type = orderData.getLogisticsType();
            BigDecimal skuAmount = BigDecimal.ZERO;
            if (LogisticsTypeEnum.PickUp.equals(type)) {
                skuAmount = orderData.getOriginalTotalPickUpPrice();
            } else if (LogisticsTypeEnum.DropShipping.equals(type)) {
                skuAmount = orderData.getOriginalTotalDropShippingPrice();
            }
            inProductItems.setSkuAmount(skuAmount.doubleValue());

            inCreateSaleOrder.setProductItems(CollUtil.newArrayList(inProductItems));

            // 自提需要传单号和附件过去恒健仓库
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                if (!(ObjectUtil.equals("D8RHYF3",orderData.getTenantId()))){
                    Warehouse warehouse = warehouseService.queryByWarehouseSystemCode(one.getWarehouseSystemCode());
                    if (ObjectUtil.isNotNull(warehouse)){
                        inCreateSaleOrder.setWarehouseCode(warehouse.getWarehouseCode());
                    }else {
                        throw new OrderPayException("仓库信息不存在"+one.getWarehouseSystemCode());
                    }
//                }
                if (!StrUtil.containsAnyIgnoreCase(logisticsCompanyName, "UPS", "FEDEX", "USPS")) {
                    JSONObject carrierSwitch = businessParameterService.getValueFromJSONObject(BusinessParameterType.HENGJIAN_CARRIER_SWITCH);
                    String switchStr = carrierSwitch.getStr(logisticsCompanyName);
                    if (switchStr != null) {
                        logisticsCompanyName = switchStr;
                    }
                }
                List<InAttachInfo> inUploadAttachments = new ArrayList<>();
                // 第三方账户为空，需要传物流单号和附件
                if (StrUtil.isBlank(logisticsAccount)) {
                    List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                    List<OrderAttachment> orderAttachments = iOrderAttachmentService.getByOrderNos(Collections.singletonList(orderNo));

                    for (OrderAttachment orderAttachment : orderAttachments) {
                        InputStream shippingLabel = OSSObtainEvent.obtainFileInputStream(orderAttachment.getAttachmentShowUrl());
//                        String base64 = PDFUtil.convertPdfToBase64(shippingLabel);
                        if (shippingLabel == null) {
                            throw new OrderPayException(OrderStatusCodeEnum.ORDER_SHIPPING_LABEL_NOT_EXISTS);
                        }
                        InAttachInfo inAttachInfo = new InAttachInfo();
                        String logisticsTrackingNo = String.valueOf(orderAttachment.getId());
                        if(CollUtil.isNotEmpty(trackingList)&&trackingList.size()==1){
                            logisticsTrackingNo = trackingList.get(0).getLogisticsTrackingNo();
                        }
                        if (ChannelTypeEnum.TikTok.equals(orderItem.getChannelType()) && CollUtil.isEmpty(inUploadAttachments)&&OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderData.getOrderSource())){
                            SysOss sysOss = ossMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, orderData.getLineOrderItemId()));
                            logisticsTrackingNo = sysOss.getBusinessNumber();

                            inAttachInfo.setName(logisticsTrackingNo);

                        }

                        inAttachInfo.setName(logisticsTrackingNo);
                        if(OrderAttachmentTypeEnum.ShippingLabel.equals(orderAttachment.getAttachmentType())||ObjectUtil.isEmpty(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(0);
                        }

                        if(OrderAttachmentTypeEnum.BOL.equals(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(2);
                        }
                        if(OrderAttachmentTypeEnum.CartonLabel.equals(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(4);
                        }
                        if(OrderAttachmentTypeEnum.PalletLabel.equals(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(5);
                        }
                        if(OrderAttachmentTypeEnum.ItemLabel.equals(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(6);
                        }
                        if(OrderAttachmentTypeEnum.Other.equals(orderAttachment.getAttachmentType())){
                            inAttachInfo.setType(3);
                        }
                        if(OrderAttachmentTypeEnum.Zip.equals(orderAttachment.getAttachmentType())){
                            continue;
                        }
                        // 去除base64逻辑改为url
                        inAttachInfo.setUrl(orderAttachment.getAttachmentShowUrl());
                        inUploadAttachments.add(inAttachInfo);
                    }

                    if (CollUtil.isEmpty(inUploadAttachments)) {
                        log.error("恒健仓库【创建发货单】 附件为空,订单号:{}", orderNo);
                        throw new OrderPayException(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR);
                    }
                    if(OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)&& "LTL".equals(logisticsCompanyName)){
                        // 如果inUploadAttachments的元素内没有type为2的元素,返回true
                        boolean isHaveBol = inUploadAttachments.stream()
                                                               .noneMatch(attachInfo -> Objects.equals(attachInfo.getType(), OrderAttachmentTypeEnum.BOL.getCode()));
                        if(isHaveBol){
                            log.info("恒健仓库【创建发货单】,open-api订单,空白BOL附件,临时空pdf补偿");
                            InAttachInfo inAttachInfo = new InAttachInfo();
                            inAttachInfo.setName("emptyBOL");
                            inAttachInfo.setType(OrderAttachmentTypeEnum.BOL.getCode());
                            inAttachInfo.setUrl("https://hengjian-distribution.oss-cn-hangzhou.aliyuncs.com/2025/01/09/02d9f4b015a24a2abfb3d5f205583315.pdf");
                            inUploadAttachments.add(inAttachInfo);
                        }

                    }
                    inCreateSaleOrder.setAttachInfoItems(inUploadAttachments);

                    log.info("CarrierCode value: {}", inCreateSaleOrder.getCarrierCode());
                    //无附件 不传CarrierCode 有附件 传WLC
                    if (!CollectionUtils.isEmpty(inUploadAttachments)) {
                        //String bizArkCarrierCode = "WLC-" + logisticsCompanyName.toUpperCase();
                        //inCreateSaleOrder.setCarrierCode(bizArkCarrierCode);
                        if (!CollectionUtils.isEmpty(inUploadAttachments)) {
                            carrierMethod(isNeedLabeling, orderSource, logisticsCompanyName, inCreateSaleOrder, orderItem);
                        }

                        log.info("CarrierCode value2: {}", inCreateSaleOrder.getCarrierCode());
                    }
                }
                // 亚马逊VC传AMSP
//                if (ChannelTypeEnum.Amazon_VC.equals(orderItem.getChannelType())) {
//                    inCreateSaleOrder.setCarrierCode("AMSP");
//                }
            }
            // 一件代发,需要确认承运商
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                Target walmart 这两个渠道指定 FEDEX
                ChannelTypeEnum channelType = orderData.getChannelType();
                boolean result = ChannelTypeEnum.Target.equals(channelType) || ChannelTypeEnum.Walmart.equals(channelType);
                if(result){
                    inCreateSaleOrder.setCarrier("FEDEX");
                }
//                if (StrUtil.containsAnyIgnoreCase(logisticsCompanyName, "UPS", "FEDEX", "USPS")) {
//                    inCreateSaleOrder.setCarrier(logisticsCompanyName);
//                }
                if(approvedTenant){
                    Warehouse warehouse = TenantHelper.ignore(()->iWarehouseService.getOne(new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseSystemCode, one.getWarehouseSystemCode())));
                    warehouseCode =warehouse.getWarehouseCode();
                    // 如果是测算流程需要指定仓库和发运商
                    inCreateSaleOrder.setWarehouseCode(warehouseCode);
                    if(!result){
                        inCreateSaleOrder.setCarrier(orderLogisticsInfo.getLogisticsCarrierCode());
//                        inCreateSaleOrder.setCarrierCode(orderLogisticsInfo.getLogisticsCarrierCode());
                    }
                }
            }
            // 添加租户id和公司名称
            inCreateSaleOrder.setDistributionCustomId(orderData.getTenantId());
            if(null != sysTenantVo){
                inCreateSaleOrder.setDistributionCustomName(sysTenantVo.getCompanyName());
            }
            // 订单是测算订单的话,需要
            log.info("推送json"+JSONUtil.toJsonStr(inCreateSaleOrder));
            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            delegate.orderApi().createSaleOrder(inCreateSaleOrder);
            inCreateSaleOrder.setAttachInfoItems(null);
            shippingRecord.setInCreateJson(JSONUtil.parseObj(inCreateSaleOrder));
            shippingRecord.setShippingState(ShippingStateEnum.Unshipped);
            orderData.setChannelReceiptStatus("Success");
            orderData.setExceptionCode(0);
            orderData.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderData.setChannelOrderName(channelOrderNo);
        } catch (OrderPayException e) {
            log.info("恒健仓库【创建发货单】 发生异常（RStatusCodeException） {}", e.getMessage(), e);
            shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
            shippingOrderState = ShippingOrderStateEnum.CreateFailed;
            String message = e.getMessage();
            if(StrUtil.isNotEmpty(message)){
                shippingRecord.setShippingErrorCode(message);
            }else {
                shippingRecord.setShippingErrorCode("创建发货单异常");
            }

            shippingRecord.setShippingErrorMessage(e.getLocaleMessage().toJSON());
            orderData.setFulfillmentProgress(LogisticsProgress.Abnormal);
            orderData.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
            // todo 支付异常
//            iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getOrderNo, orderNo)
//                                                                   .set(Orders::getFulfillmentProgress, LogisticsProgress.Abnormal));
        } catch (Exception e) {
            log.info("恒健仓库【创建发货单】 发生异常 {}", e.getMessage(), e);
            shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
            shippingOrderState = ShippingOrderStateEnum.CreateFailed;
            String message = e.getMessage();
            if(StrUtil.isNotEmpty(message)){
                shippingRecord.setShippingErrorCode(message);
            }else {
                shippingRecord.setShippingErrorCode("创建发货单异常");
            }

            shippingRecord.setShippingErrorMessage(OrderStatusCodeEnum.THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR.toJSON());
            orderData.setFulfillmentProgress(LogisticsProgress.Abnormal);
            orderData.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
            // todo 未知异常
        } finally {
            TenantHelper.ignore(() -> iOrderItemShippingRecordService.updateById(shippingRecord));
            iOrdersService.updateById(orderData);
//            if(isHengjian){
            iOrderItemService.updateShippingOrderState(orderItemNo, shippingOrderState);
//            }
        }

    }

    /**
     * 功能描述：非 ltl 承运商处理器
     *
     * @param orderItem            订单项目
     * @param logisticsCompanyName 物流公司名称
     * @param inCreateSaleOrder    创建销售订单
     * <AUTHOR>
     * @date 2025/03/19
     */
    private static void nonLtlCarrierHandler(OrderItem orderItem, String logisticsCompanyName,
                                  InCreateSaleOrder inCreateSaleOrder) {
        if (ChannelTypeEnum.Amazon_VC.equals(orderItem.getChannelType())) {
            String logisticsCarrier = logisticsCompanyName.toUpperCase();
            String logisticsCarrierCode = logisticsCompanyName.toUpperCase();
            if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "FED")) {
                logisticsCarrier = "WLC-" + "FedEx";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "UPS")) {
                logisticsCarrier = "WLC-" + "UPS";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMXL")) {
                logisticsCarrier = "AMXL";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMZL")) {
                logisticsCarrier = "AMZL";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMSP")) {
                logisticsCarrier = "AMSP";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "LTL")) {
                logisticsCarrier = "FBALTL";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "ONTRAC")) {
                logisticsCarrier = "WLC-" + "OnTrac";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "USPS")) {
                logisticsCarrier = "WLC-" + "UPS";
            }else {
                logisticsCarrier = "WLC-" + logisticsCompanyName.toUpperCase();
            }
            inCreateSaleOrder.setCarrierCode(logisticsCarrier);
        }else {
            if (StrUtil.contains(logisticsCompanyName.toUpperCase(), "AMSP") ||
                StrUtil.contains(logisticsCompanyName.toUpperCase(), "AMZL")) {
                inCreateSaleOrder.setCarrierCode(logisticsCompanyName.toUpperCase());
            } else if(StrUtil.contains(logisticsCompanyName.toUpperCase(), "LTL")){
                inCreateSaleOrder.setCarrierCode("FBALTL");
            } else if (StrUtil.contains(logisticsCompanyName.toUpperCase(), "AMXL")) {
                inCreateSaleOrder.setCarrierCode("AMXL");
            }else {
                inCreateSaleOrder.setCarrierCode("WLC-" + logisticsCompanyName.toUpperCase());
            }

        }
    }

    /**
     * 根据发货记录查询发货单
     *
     * @param shippingNoList
     */
    @Override
    public void queryOrderByShippingRecord(List<String> shippingNoList) {
        log.info("恒健仓库【查询发货单】 需要查询的记录数量 {}", CollUtil.size(shippingNoList));
        List<OrderItemShippingRecord> shippingRecordList = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNoList);
        // 渠道订单号集合
        List<String> channelOrderNoList = new ArrayList<>();
        // 多渠道tracking信息集合
        List<TrackingCallBackVo> trackingCallBackVoList = new ArrayList<>();
        // erp订单状态集合
        Map<String,ShippingStateEnum> orderStatusMap = new HashMap<>();
        if (CollUtil.isNotEmpty(shippingRecordList)) {
            for (OrderItemShippingRecord shippingRecord : shippingRecordList) {
                String orderNo = shippingRecord.getOrderNo();
                String orderItemNo = shippingRecord.getOrderItemNo();
                String shippingNo = shippingRecord.getShippingNo();

                OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

                LogisticsProgress fulfillmentProgress = orderItem.getFulfillmentProgress();
                // 查询关联的子订单是否已经发货，如果发货，则此ShippingOrders不再需要定时器轮询
                if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched) || ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Fulfilled)) {
                    shippingRecord.setSystemManaged(false);
                    iOrderItemShippingRecordService.updateById(shippingRecord);
                    continue;
                }

                ShippingStateEnum oldShippingState = shippingRecord.getShippingState();
                String supplierTenantId = shippingRecord.getSupplierTenantId();

                if (shippingRecord.getSystemManaged() == null) {
                    shippingRecord.setSystemManaged(true);
                }
                Orders order = iOrdersService.getByOrderNo(orderNo);
                //查询当前订单是否是LTL订单
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);

                try {
                    ThebizarkDelegate delegate = initDelegate(supplierTenantId);
                    Result<OutOrder> result;
                    try {
                        log.info("查询ERP订单详情订单号:"+order.getOrderExtendId());
                         result = delegate.orderApi().getOne(order.getOrderExtendId());
                    }catch (ThebizarkException thebizarkException){
                        log.info("使用orderNo获取订单信息出现异常,{}",thebizarkException.getMessage());
                        log.info("使用shippingNo获取订单信息,{} ",shippingNo);
                        result = delegate.orderApi().getOne(shippingNo);
                    }
                    OutOrder outOrder = result.getData();

                    if (outOrder != null) {
                        String errorCode = outOrder.getErrorCode();
                        // 添加erp的发货异常（购买面单异常，订单成本异常，发货单取消异常，订单更新异常，商品无法关联，无异常 （除了这些 剩余的异常都要））
                        List<String> errorCodeList = Arrays.asList("unexception", "purchaseException", "costException", "deliverOrderCancelException", "updateException","produceException");
                        if(StringUtils.isNotBlank(errorCode) && !errorCodeList.contains(errorCode)){
                            order.setShipmentException(errorCode);
                            iOrdersService.updateById(order);
                        }else {
                            order.setShipmentException(null);
                            iOrdersService.getBaseMapper().updateShipmentExceptionCodeNullById(order.getId());
                        }
                        // errorCode为空，或者为unexception，此情况为无异常，需要走后续操作
                        if (StringUtils.isBlank(errorCode) || StringUtils.equals(errorCode, "unexception")) {
                            String orderStatusEnum = outOrder.getOrderStatusEnum();
                            String warehouseCode = outOrder.getWarehouseCode();
                            //更新订单明细表
                            order.setLatestShipDate(outOrder.getLatestShipDate());
                            order.setWarehouseExpectedShipDate(outOrder.getWarehouseExpectedShipDate());
                            Warehouse warehouse = iWarehouseService.queryByWarehouseType(supplierTenantId, WarehouseTypeEnum.BizArk, warehouseCode);
                            String warehouseSystemCode = warehouseCode;
                            if (warehouse != null) {
                                warehouseSystemCode = warehouse.getWarehouseSystemCode();
                            }

                            if (StringUtils.isBlank(orderStatusEnum)) {
                                // 恒健仓库订单状态为空，跳过不处理
                                continue;
                            }

                            ShippingStateEnum shippingState = ShippingStateEnum.valueOfByBizArk(orderStatusEnum);
                            // 取消的发货单停止定时器轮询
                            if (ShippingStateEnum.Canceled.equals(shippingState)) {
                                shippingRecord.setSystemManaged(false);
                                iOrderItemShippingRecordService.updateById(shippingRecord);
                                continue;
                            }
                            // 记录订单状态
                            if(!orderStatusMap.containsKey(outOrder.getOrderNo())){
                                orderStatusMap.put(outOrder.getOrderNo(),shippingState);
                            }
                            // 已发货的才继续往下走
                            // 2022-8-2调整，已拣货的也继续往下走，但是只同步物流跟踪信息，不修改状态为已发货
                            // 当前查询的恒健仓库物流单状态为已拣货或已发货，且与之前的ShippingOrders的老状态不相等时，才进入if处理跟踪单数据
                            if ((ObjectUtil.equals(shippingState, ShippingStateEnum.Picked) || ObjectUtil.equals(shippingState,
                                ShippingStateEnum.Shipped)) && ObjectUtil.notEqual(shippingState, oldShippingState)) {
                                List<OutShipment> shipmentPackages = outOrder.getShipmentPackages();

                                // log.info("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}", shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));
                                // XxlJobHelper.log("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}", shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));
                                if (shipmentPackages != null) {
                                    // 旧的跟踪单信息，需要全删全增
                                    // List<OrderItemTrackingRecord> oldTrackingOrders = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                                    // log.info("queryBizarkTacking - trackingOrders.size = {}", CollectionUtils.size(oldTrackingOrders));
                                    // 多渠道回调信息
                                    TrackingCallBackVo trackingCallBackVo = new TrackingCallBackVo();
                                    // 需要新创建的物流单
                                    List<OrderItemTrackingRecord> newTrackingRecords = new ArrayList<>();
                                    //LTL的PO订单,创建发货单方式是一单多品,Tracking回传 多品采用相同的tracking
                                    if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name().equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
                                        shipmentPackages=List.of(shipmentPackages.get(0));
                                    }
                                    for (OutShipment shipmentPackage : shipmentPackages) {
                                        if (Objects.equals(shippingState, ShippingStateEnum.Shipped) && shipmentPackage.getPackageFlag() != 0) {
                                            // 包裹状态不等于0，跳过，恒健仓库包裹状态为1的才是有效包裹
                                            continue;
                                        }
                                        // 将恒健仓库物流信息转成物流信息
                                        OrderItemTrackingRecord trackingRecord = this.convertTrackingRecord(shipmentPackage, shippingRecord,
                                            orderItem, orderItemProductSku, warehouseSystemCode, shippingState);

                                        // 添加到待保存的列表中
                                        newTrackingRecords.add(trackingRecord);
                                    }
                                    if(StringUtils.isNotEmpty(order.getChannelOrderNo()) ){
                                        if(null != order.getChannelId()){
                                            if(null == order.getTrackingFlag() || order.getTrackingFlag().equals(1)){
                                                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.selectByIdNotTenant(order.getChannelId());
                                                List<String> channelNames = Arrays.asList("FineJoys Home", "MyDepot Outlet", "Mydepot3-US","Tik Tok-MyDepot4店-US");
                                                boolean channelNameFlag = channelNames.contains(tenantSalesChannel.getChannelName());

                                                boolean channelFlag = StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr())
                                                    && null != tenantSalesChannel.getState()
                                                    && tenantSalesChannel.getState().equals(1)
                                                    || channelNameFlag;
//                                                if(null != tenantSalesChannel && StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr()) && null != tenantSalesChannel.getState() && tenantSalesChannel.getState().equals(1)){
                                                if(null != tenantSalesChannel && channelFlag){
                                                if(StringUtils.isNotEmpty(order.getChannelOrderNo()) && !channelOrderNoList.contains(order.getChannelOrderNo())){
                                                        if(orderStatusEnum.equals("shipped")){
                                                            trackingCallBackVo.setOrderNum(order.getChannelOrderNo()).setChannelId(tenantSalesChannel.getChannelName()).setShipWarehouse(warehouseSystemCode);
                                                            trackingCallBackVoList.add(trackingCallBackVo);
                                                            log.info("回传tracking订单：{}",trackingCallBackVo);
                                                            channelOrderNoList.add(order.getChannelOrderNo());
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    shippingRecord.setShippingState(shippingState);
                                    // 新的跟踪单集合不为空，保存新的跟踪单，并把旧的跟踪单都解除关联
                                    if (CollectionUtils.isNotEmpty(newTrackingRecords)) {
                                        iOrderItemTrackingRecordService.trackingListDisassociate(orderItemNo);
                                        iOrderItemTrackingRecordService.saveBatch(newTrackingRecords);
                                    }

                                    // // 子订单不为空，开始回传物流信息至渠道
                                    // log.info("queryBizarkTacking - newTrackingRecords.size = {} saveOrderItems.size = {} shippingStatusType = {}",
                                    //     CollectionUtils.size(newTrackingRecords), CollectionUtils.size(saveOrderItems), shippingStatusType);
                                    if (Objects.equals(shippingState, ShippingStateEnum.Shipped)) {
                                        // 恒健仓库回传状态为已发货或已拣货，并且存在物流商和单号时，该出货单不再需要定时器轮询
                                        shippingRecord.setSystemManaged(false);
                                        shippingRecord.setShippingErrorCode(null);
                                        shippingRecord.setShippingErrorMessage(null);

                                        // 同步物流信息至第三方渠道
                                        ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();
                                        thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
                                        thirdChannelFulfillmentRecord.setChannelType(order.getChannelType());
                                        thirdChannelFulfillmentRecord.setOrderNo(order.getOrderExtendId());
                                        thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
                                        thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
                                        thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
                                        thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
                                        thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
                                        iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
                                    }
                                }
                            }
                        } else {
                            // insufficientInventoryException库存不足异常
                            // 发生任何形式的异常，都要保持定时器运行
                            shippingRecord.setShippingErrorCode(errorCode);
                            shippingRecord.setSystemManaged(true);
                        }
                    }
                } catch (Exception e) {
                    shippingRecord.setShippingErrorCode(e.getMessage());
                    log.error("恒健仓库【查询发货单】 发货单号{}，发生未知错误 {},{}", shippingNo, e.getMessage(), e);
                }
                iOrdersService.updateById(order);
                iOrderItemShippingRecordService.updateById(shippingRecord);
                iOrderItemService.updateById(orderItem);
                iOrderItemProductSkuService.updateById(orderItemProductSku);
                SetOrderFulfillmentProgressEvent event = new SetOrderFulfillmentProgressEvent(order.getId());
                SpringUtils.publishEvent(event);
            }
            log.info("订单tracking信息回传头信息: {}",JSONUtil.toJsonStr(trackingCallBackVoList));
            if(CollectionUtil.isNotEmpty(trackingCallBackVoList)){
                List<String> orderNums = trackingCallBackVoList.stream()
                                           .map(TrackingCallBackVo::getOrderNum)
                                           .collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(orderNums)){
                    List<Orders> channelOrderNoIn = iOrdersService.findByChannelOrderNoIn(orderNums);
                    List<OrderItemTrackingRecord> listByOrderNo = new ArrayList<>();
                    List<com.zsmall.order.entity.domain.OrderItem> orderItemList = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn)){
                        List<String> orderNos = channelOrderNoIn.stream()
                                                                .map(Orders::getOrderNo)
                                                                .collect(Collectors.toList());
                        listByOrderNo = iOrderItemTrackingRecordService.getListByOrderNo(orderNos);
                        orderItemList = iOrderItemService.getList(orderNos);
                    }
                    // 没有tracking信息的订单号
                    List<TrackingCallBackVo> trackingCallBackVoListNotCallBack = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn) && CollectionUtil.isNotEmpty(listByOrderNo)){
                   loop2: for(TrackingCallBackVo trackingCallBackVo : trackingCallBackVoList){
                            String shipDate = null;
                            List<TrackingCallBackPackagesVo> packages = new ArrayList<>();
                            loop: for(Orders orders : channelOrderNoIn){
                                if(trackingCallBackVo.getOrderNum().equals(orders.getChannelOrderNo())){
                                    for(OrderItemTrackingRecord orderItemTrackingRecord : listByOrderNo){
                                        if(orders.getOrderNo().equals(orderItemTrackingRecord.getOrderNo())){
                                            TrackingCallBackPackagesVo trackingCallBackPackagesVo = new TrackingCallBackPackagesVo();
                                            // 判断当前的订单的发货状态，如果不为shipped状态，则不能回传
                                            if(CollUtil.isNotEmpty(orderStatusMap)){
                                                ShippingStateEnum orderStatue = orderStatusMap.get(orders.getOrderExtendId());
                                                if(null != orderStatue && !ObjectUtil.equals(orderStatue, ShippingStateEnum.Shipped)){
                                                    log.warn("订单状态为:{}，订单信息:{},不能回传",orderStatue, orders);
                                                    trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                    continue loop2;
                                                }
                                            }
                                            // trackingNo为空或者渠道商为空的排除
                                            if(StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsCarrier()) || StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsTrackingNo())){
                                                log.warn("订单tracking信息为空，订单tracking信息:{},",orderItemTrackingRecord);
                                                trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                continue loop2;
                                            }
                                            trackingCallBackPackagesVo.setCarrier(orderItemTrackingRecord.getLogisticsCarrier()).setTrackingNo(orderItemTrackingRecord.getLogisticsTrackingNo());
                                              if(null != orderItemTrackingRecord.getDispatchedTime()){
                                                  String shipDate1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderItemTrackingRecord.getDispatchedTime());
                                                  trackingCallBackPackagesVo.setShipDate(shipDate1);
                                                  if(null == shipDate){
                                                      shipDate = shipDate1;
                                                  }
                                              }
                                            TrackingCallBackPackagesItemsVo trackingCallBackPackagesItemsVo = new TrackingCallBackPackagesItemsVo();
                                            // 设置包裹重量
                                            for(com.zsmall.order.entity.domain.OrderItem orderItem : orderItemList){
                                                if(orders.getOrderNo().equals(orderItem.getOrderNo())){
                                                    ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItem.getProductSkuCode());
                                                    if(null != productSkuDetail){
                                                        switch (productSkuDetail.getPackWeightUnit()){
                                                            case lb:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.poundsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case kg:
                                                                trackingCallBackPackagesVo.setPackageWeight(productSkuDetail.getPackWeight());
                                                                break;
                                                            case g:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.gramsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case t:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                        }
                                                    }
                                                    trackingCallBackPackagesItemsVo.setSellerSku(orderItem.getChannelSku());
                                                    break;
                                                }
                                            }
                                            trackingCallBackPackagesItemsVo.setLineNum(orders.getLineOrderItemId()).setShipedQuantity(orderItemTrackingRecord.getQuantity()).setSellerSku(orderItemTrackingRecord.getSku());
                                            trackingCallBackPackagesVo.setItems(Arrays.asList(trackingCallBackPackagesItemsVo));
                                            packages.add(trackingCallBackPackagesVo);
                                            continue loop;
                                        }
                                    }
                                }
                            }
                            trackingCallBackVo.setPackages(packages);
                            trackingCallBackVo.setShipDate(shipDate);
                        }
                        // 去除没有tracking信息的订单
                        log.info("没有tracking信息的订单: {}, size: {}",JSONUtil.toJsonStr(trackingCallBackVoListNotCallBack),trackingCallBackVoListNotCallBack.size());
                        if(CollUtil.isNotEmpty(trackingCallBackVoListNotCallBack)){
                            trackingCallBackVoList.removeAll(trackingCallBackVoListNotCallBack);
                        }
                        try {
                            log.info("订单回传tracking信息至多渠道的全部tracking信息: {}",JSONUtil.toJsonStr(trackingCallBackVoList));
                            Integer batchSize = 1000;
                            Iterator<TrackingCallBackVo> iterator = trackingCallBackVoList.iterator();
                            List<TrackingCallBackVo> batch = new ArrayList<>(batchSize);
                            while (iterator.hasNext()) {
                                TrackingCallBackVo trackingCallBackVo = iterator.next();
                                batch.add(trackingCallBackVo);
                                if (batch.size() == batchSize) {
                                    log.info("订单回传tracking信息至多渠道,tracking信息: {}",JSONUtil.toJsonStr(batch));
                                    String str = JSON.toJSONString(batch);
                                    String messageId = IdUtil.fastSimpleUUID();
                                    Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                    rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,message);
                                    List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                .collect(Collectors.toList());
                                    if(CollUtil.isNotEmpty(orderNumList)){
                                        iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                    }
                                    // 清空列表
                                    batch.clear();
                                }
                            }
                            // 剩余的
                            if (!batch.isEmpty()) {
                                log.info("订单回传tracking信息至多渠道,tracking信息: {}",JSONUtil.toJsonStr(batch));
                                String str = JSON.toJSONString(batch);
                                String messageId = IdUtil.fastSimpleUUID();
                                Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,message);
                                List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                 .collect(Collectors.toList());
                                if(CollUtil.isNotEmpty(orderNumList)){
                                    iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                }
                                // 清空批次列表
                                batch.clear();
                            }
                        }catch (Exception e){
                            log.info("订单回传tracking信息至多渠道,tracking信息: {}, 出现异常: {}",JSONUtil.toJsonStr(trackingCallBackVoList),e.getMessage());
                        }
                    }
                }
            }
        }
    }
    /**
     * 根据发货记录查询发货单,需要加补偿方案
     *
     * @param shippingNoList
     */
    @Override
    public void queryOrderByShippingRecordForUploadTracking(List<String> shippingNoList,Map<String,List<OutShipment>>outShipmentMap) {
        log.info("tracking回传的订单数量 {}", CollUtil.size(shippingNoList));
        List<OrderItemShippingRecord> shippingRecordList = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNoList);
        // 渠道订单号集合
        List<String> channelOrderNoList = new ArrayList<>();
        // 多渠道tracking信息集合
        List<TrackingCallBackVo> trackingCallBackVoList = new ArrayList<>();
        // erp订单状态集合
        Map<String,ShippingStateEnum> orderStatusMap = new HashMap<>();
        if (CollUtil.isNotEmpty(shippingRecordList)) {
            for (OrderItemShippingRecord shippingRecord : shippingRecordList) {
                String orderNo = shippingRecord.getOrderNo();
                String orderItemNo = shippingRecord.getOrderItemNo();
                String shippingNo = shippingRecord.getShippingNo();
                Orders order = iOrdersService.getByOrderNo(orderNo);
                OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

                LogisticsProgress fulfillmentProgress = order.getFulfillmentProgress();
                // 查询关联的子订单是否已经发货，如果发货，则此ShippingOrders不再需要定时器轮询
                log.info("订单:{}回传时的发货状态-{}",orderNo,fulfillmentProgress);
                if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched) || ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Fulfilled)) {
                    shippingRecord.setSystemManaged(false);
                    iOrderItemShippingRecordService.updateById(shippingRecord);
                    continue;
                }

                ShippingStateEnum oldShippingState = shippingRecord.getShippingState();
                String supplierTenantId = shippingRecord.getSupplierTenantId();

                if (shippingRecord.getSystemManaged() == null) {
                    shippingRecord.setSystemManaged(true);
                }

                //查询当前订单是否是LTL订单
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);

                try {
//                    ThebizarkDelegate delegate = initDelegate(supplierTenantId);
//                    Result<OutOrder> result;
//                    try {
//                        log.info("查询ERP订单详情订单号:"+order.getOrderExtendId());
//                        result = delegate.orderApi().getOne(order.getOrderExtendId());
//                    }catch (ThebizarkException thebizarkException){
//                        log.info("使用orderNo获取订单信息出现异常,{}",thebizarkException.getMessage());
//                        log.info("使用shippingNo获取订单信息,{} ",shippingNo);
//                        result = delegate.orderApi().getOne(shippingNo);
//                    }
//                    OutOrder outOrder = result.getData();
                    // 走的tracking录入不会调用erp接口
                    String errorCode = null;
                    // 添加erp的发货异常（购买面单异常，订单成本异常，发货单取消异常，订单更新异常，商品无法关联，无异常 （除了这些 剩余的异常都要））
                    List<String> errorCodeList = Arrays.asList("unexception", "purchaseException", "costException", "deliverOrderCancelException", "updateException","produceException");
                    if(StringUtils.isNotBlank(errorCode) && !errorCodeList.contains(errorCode)){
                        order.setShipmentException(errorCode);
                        iOrdersService.updateById(order);
                    }else {
                        order.setShipmentException(null);
                        iOrdersService.getBaseMapper().updateShipmentExceptionCodeNullById(order.getId());
                    }

                    // errorCode为空，或者为unexception，此情况为无异常，需要走后续操作
                    if (StringUtils.isBlank(errorCode) || StringUtils.equals(errorCode, "unexception")) {

                        String warehouseCode = shippingRecord.getWarehouseCode();
                        //和赛赛沟通后的结果是先空着
//                        order.setLatestShipDate(outOrder.getLatestShipDate());
//                        order.setWarehouseExpectedShipDate(outOrder.getWarehouseExpectedShipDate());

                        Warehouse warehouse = iWarehouseService.queryByWarehouseType(supplierTenantId, WarehouseTypeEnum.BizArk, warehouseCode);
                        String warehouseSystemCode = warehouseCode;
                        if (warehouse != null) {
                            warehouseSystemCode = warehouse.getWarehouseSystemCode();
                        }


                        ShippingStateEnum shippingState = ShippingStateEnum.Shipped;
                        // 取消的发货单停止定时器轮询
                        if (ShippingStateEnum.Canceled.equals(shippingState)) {
                            shippingRecord.setSystemManaged(false);
                            iOrderItemShippingRecordService.updateById(shippingRecord);
                            continue;
                        }
                        // 记录订单状态
                        if(!orderStatusMap.containsKey(shippingRecord.getOrderNo())){
                            orderStatusMap.put(shippingRecord.getOrderNo(),shippingState);
                        }
                        // 已发货的才继续往下走
                        // 2022-8-2调整，已拣货的也继续往下走，但是只同步物流跟踪信息，不修改状态为已发货
                        // 当前查询的恒健仓库物流单状态为已拣货或已发货，且与之前的ShippingOrders的老状态不相等时，才进入if处理跟踪单数据
                        if ((ObjectUtil.equals(shippingState, ShippingStateEnum.Picked) || ObjectUtil.equals(shippingState,
                            ShippingStateEnum.Shipped)) && ObjectUtil.notEqual(shippingState, oldShippingState)) {
                            // shipmentPackages需要手动造一个
                            List<OutShipment> shipmentPackages = outShipmentMap.get(orderNo);

                            // log.info("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}", shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));
                            // XxlJobHelper.log("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}", shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));
                            if (shipmentPackages != null) {
                                // 旧的跟踪单信息，需要全删全增
                                // List<OrderItemTrackingRecord> oldTrackingOrders = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                                // log.info("queryBizarkTacking - trackingOrders.size = {}", CollectionUtils.size(oldTrackingOrders));
                                // 多渠道回调信息
                                TrackingCallBackVo trackingCallBackVo = new TrackingCallBackVo();
                                // 需要新创建的物流单
                                List<OrderItemTrackingRecord> newTrackingRecords = new ArrayList<>();
                                //LTL的PO订单,创建发货单方式是一单多品,Tracking回传 多品采用相同的tracking
                                if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name().equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
                                    shipmentPackages=List.of(shipmentPackages.get(0));
                                }
                                for (OutShipment shipmentPackage : shipmentPackages) {
                                    if (Objects.equals(shippingState, ShippingStateEnum.Shipped) && shipmentPackage.getPackageFlag() != 0) {
                                        // 包裹状态不等于0，跳过，恒健仓库包裹状态为1的才是有效包裹
                                        continue;
                                    }
                                    // 将恒健仓库物流信息转成物流信息
                                    OrderItemTrackingRecord trackingRecord = this.convertTrackingRecord(shipmentPackage, shippingRecord,
                                        orderItem, orderItemProductSku, warehouseSystemCode, shippingState);

                                    // 添加到待保存的列表中
                                    newTrackingRecords.add(trackingRecord);
                                }
                                if(StringUtils.isNotEmpty(order.getChannelOrderNo()) ){
                                    if(null != order.getChannelId()){
                                        if(null == order.getTrackingFlag() || order.getTrackingFlag().equals(1)){
                                            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.selectByIdNotTenant(order.getChannelId());
                                            List<String> channelNames = Arrays.asList("FineJoys Home", "MyDepot Outlet", "Mydepot3-US","Tik Tok-MyDepot4店-US");
                                            boolean channelNameFlag = channelNames.contains(tenantSalesChannel.getChannelName());

                                            boolean channelFlag = StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr())
                                                && null != tenantSalesChannel.getState()
                                                && tenantSalesChannel.getState().equals(1)
                                                || channelNameFlag;
                                            log.info("订单:{}回传,channelNameFlag={},channelFlag={},state={}",orderNo,channelNameFlag,channelFlag,tenantSalesChannel.getState());
                                            if(null != tenantSalesChannel && channelFlag){
                                                if(StringUtils.isNotEmpty(order.getChannelOrderNo()) && !channelOrderNoList.contains(order.getChannelOrderNo())){
                                                    trackingCallBackVo.setOrderNum(order.getChannelOrderNo()).setChannelId(tenantSalesChannel.getChannelName()).setShipWarehouse(warehouseSystemCode);
                                                    trackingCallBackVoList.add(trackingCallBackVo);
                                                    log.info("回传tracking订单：{}",trackingCallBackVo);
                                                    channelOrderNoList.add(order.getChannelOrderNo());
                                                }
                                            }
                                        }
                                    }
                                }

                                shippingRecord.setShippingState(shippingState);
                                // 新的跟踪单集合不为空，保存新的跟踪单，并把旧的跟踪单都解除关联
                                if (CollectionUtils.isNotEmpty(newTrackingRecords)) {
                                    iOrderItemTrackingRecordService.trackingListDisassociate(orderItemNo);
                                    iOrderItemTrackingRecordService.saveBatch(newTrackingRecords);
                                }

                                // // 子订单不为空，开始回传物流信息至渠道
                                // log.info("queryBizarkTacking - newTrackingRecords.size = {} saveOrderItems.size = {} shippingStatusType = {}",
                                //     CollectionUtils.size(newTrackingRecords), CollectionUtils.size(saveOrderItems), shippingStatusType);
                                if (Objects.equals(shippingState, ShippingStateEnum.Shipped)) {
                                    // 恒健仓库回传状态为已发货或已拣货，并且存在物流商和单号时，该出货单不再需要定时器轮询
                                    shippingRecord.setSystemManaged(false);
                                    shippingRecord.setShippingErrorCode(null);
                                    shippingRecord.setShippingErrorMessage(null);

                                    // 同步物流信息至第三方渠道
                                    ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();
                                    thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
                                    thirdChannelFulfillmentRecord.setChannelType(order.getChannelType());
                                    thirdChannelFulfillmentRecord.setOrderNo(order.getOrderExtendId());
                                    thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
                                    thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
                                    thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
                                    thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
                                    thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
                                    iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
                                }
                            }
                        }
                    } else {
                        // insufficientInventoryException库存不足异常
                        // 发生任何形式的异常，都要保持定时器运行
                        shippingRecord.setShippingErrorCode(errorCode);
                        shippingRecord.setSystemManaged(true);
                    }
                } catch (Exception e) {
                    shippingRecord.setShippingErrorCode(e.getMessage());
                    log.error("tracking导入回传多渠道 发货单号{}，发生未知错误 {},{}", shippingNo, e.getMessage(), e);
                }
                iOrdersService.updateById(order);
                iOrderItemShippingRecordService.updateById(shippingRecord);
                iOrderItemService.updateById(orderItem);
                iOrderItemProductSkuService.updateById(orderItemProductSku);
                // tracking导入的时候不能再更新订单和子订单的收获状态了
                SetOrderFulfillmentProgressEvent event = new SetOrderFulfillmentProgressEvent(order.getId());
                SpringUtils.publishEvent(event);
            }
            log.info("订单tracking信息回传头信息: {}",JSONUtil.toJsonStr(trackingCallBackVoList));
            if(CollectionUtil.isNotEmpty(trackingCallBackVoList)){
                List<String> orderNums = trackingCallBackVoList.stream()
                                                               .map(TrackingCallBackVo::getOrderNum)
                                                               .collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(orderNums)){
                    List<Orders> channelOrderNoIn = iOrdersService.findByChannelOrderNoIn(orderNums);
                    List<OrderItemTrackingRecord> listByOrderNo = new ArrayList<>();
                    List<com.zsmall.order.entity.domain.OrderItem> orderItemList = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn)){
                        List<String> orderNos = channelOrderNoIn.stream()
                                                                .map(Orders::getOrderNo)
                                                                .collect(Collectors.toList());
                        listByOrderNo = iOrderItemTrackingRecordService.getListByOrderNo(orderNos);
                        orderItemList = iOrderItemService.getList(orderNos);
                    }
                    // 没有tracking信息的订单号
                    List<TrackingCallBackVo> trackingCallBackVoListNotCallBack = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn) && CollectionUtil.isNotEmpty(listByOrderNo)){
                        loop2: for(TrackingCallBackVo trackingCallBackVo : trackingCallBackVoList){
                            String shipDate = null;
                            List<TrackingCallBackPackagesVo> packages = new ArrayList<>();
                            loop: for(Orders orders : channelOrderNoIn){
                                if(trackingCallBackVo.getOrderNum().equals(orders.getChannelOrderNo())){
                                    for(OrderItemTrackingRecord orderItemTrackingRecord : listByOrderNo){
                                        if(orders.getOrderNo().equals(orderItemTrackingRecord.getOrderNo())){
                                            TrackingCallBackPackagesVo trackingCallBackPackagesVo = new TrackingCallBackPackagesVo();
                                            // 判断当前的订单的发货状态，如果不为shipped状态，则不能回传
                                            if(CollUtil.isNotEmpty(orderStatusMap)){
                                                ShippingStateEnum orderStatue = orderStatusMap.get(orders.getOrderExtendId());
                                                if(null != orderStatue && !ObjectUtil.equals(orderStatue, ShippingStateEnum.Shipped)){
                                                    log.warn("订单状态为:{}，订单信息:{},不能回传",orderStatue, orders);
                                                    trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                    continue loop2;
                                                }
                                            }
                                            // trackingNo为空或者渠道商为空的排除
                                            if(StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsCarrier()) || StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsTrackingNo())){
                                                log.warn("订单tracking信息为空，订单tracking信息:{},",orderItemTrackingRecord);
                                                trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                continue loop2;
                                            }
                                            trackingCallBackPackagesVo.setCarrier(orderItemTrackingRecord.getLogisticsCarrier()).setTrackingNo(orderItemTrackingRecord.getLogisticsTrackingNo());
                                            if(null != orderItemTrackingRecord.getDispatchedTime()){
                                                String shipDate1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderItemTrackingRecord.getDispatchedTime());
                                                trackingCallBackPackagesVo.setShipDate(shipDate1);
                                                if(null == shipDate){
                                                    shipDate = shipDate1;
                                                }
                                            }
                                            TrackingCallBackPackagesItemsVo trackingCallBackPackagesItemsVo = new TrackingCallBackPackagesItemsVo();
                                            // 设置包裹重量
                                            for(com.zsmall.order.entity.domain.OrderItem orderItem : orderItemList){
                                                if(orders.getOrderNo().equals(orderItem.getOrderNo())){
                                                    ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItem.getProductSkuCode());
                                                    if(null != productSkuDetail){
                                                        switch (productSkuDetail.getPackWeightUnit()){
                                                            case lb:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.poundsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case kg:
                                                                trackingCallBackPackagesVo.setPackageWeight(productSkuDetail.getPackWeight());
                                                                break;
                                                            case g:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.gramsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case t:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                        }
                                                    }
                                                    trackingCallBackPackagesItemsVo.setSellerSku(orderItem.getChannelSku());
                                                    break;
                                                }
                                            }
                                            trackingCallBackPackagesItemsVo.setLineNum(orders.getLineOrderItemId()).setShipedQuantity(orderItemTrackingRecord.getQuantity());
                                            trackingCallBackPackagesVo.setItems(Arrays.asList(trackingCallBackPackagesItemsVo));
                                            packages.add(trackingCallBackPackagesVo);
                                            continue loop;
                                        }
                                    }
                                }
                            }
                            trackingCallBackVo.setPackages(packages);
                            trackingCallBackVo.setShipDate(shipDate);
                        }
                        // 去除没有tracking信息的订单
                        log.info("没有tracking信息的订单: {}, size: {}",JSONUtil.toJsonStr(trackingCallBackVoListNotCallBack),trackingCallBackVoListNotCallBack.size());
                        if(CollUtil.isNotEmpty(trackingCallBackVoListNotCallBack)){
                            trackingCallBackVoList.removeAll(trackingCallBackVoListNotCallBack);
                        }
                        try {
                            log.info("订单回传tracking信息至多渠道的全部tracking信息: {}",JSONUtil.toJsonStr(trackingCallBackVoList));
                            Integer batchSize = 1000;
                            Iterator<TrackingCallBackVo> iterator = trackingCallBackVoList.iterator();
                            List<TrackingCallBackVo> batch = new ArrayList<>(batchSize);
                            while (iterator.hasNext()) {
                                TrackingCallBackVo trackingCallBackVo = iterator.next();
                                batch.add(trackingCallBackVo);
                                if (batch.size() == batchSize) {
                                    log.info("订单回传tracking信息至多渠道,tracking信息: {}",JSONUtil.toJsonStr(batch));
                                    String str = JSON.toJSONString(batch);
                                    String messageId = IdUtil.fastSimpleUUID();
                                    Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                    rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,message);
                                    List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                     .collect(Collectors.toList());
                                    if(CollUtil.isNotEmpty(orderNumList)){
                                        iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                    }
                                    // 清空列表
                                    batch.clear();
                                }
                            }
                            // 剩余的
                            if (!batch.isEmpty()) {
                                log.info("订单回传tracking信息至多渠道,tracking信息: {}",JSONUtil.toJsonStr(batch));
                                String str = JSON.toJSONString(batch);
                                String messageId = IdUtil.fastSimpleUUID();
                                Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,message);
                                List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                 .collect(Collectors.toList());
                                if(CollUtil.isNotEmpty(orderNumList)){
                                    iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                }
                                // 清空批次列表
                                batch.clear();
                            }
                        }catch (Exception e){
                            log.info("订单回传tracking信息至多渠道,tracking信息: {}, 出现异常: {}",JSONUtil.toJsonStr(trackingCallBackVoList),e.getMessage());
                        }
                    }
                }
            }
        }
    }

    /**
     * 取消发货单
     *
     * @param shippingNo
     */
    @Override
    public void cancelOrder(String shippingNo) {
        OrderItemShippingRecord shippingRecord = iOrderItemShippingRecordService.queryByShippingNoNotTenant(shippingNo);
        if (shippingRecord != null) {
            try {
                ThebizarkDelegate delegate = initDelegate(shippingRecord.getTenantId());
                Result result = delegate.orderApi().cancelOrder(shippingNo);
            } catch (Exception e) {
                log.error("恒健仓库【取消发货单】 发生异常 {}", e.getMessage(), e);
                throw new ThebizarkException(500, "取消恒健仓库发货单[" + shippingNo + "]出现未知错误");
            }
        }
    }

    @Override
    public void ordersTrackingBack(List<String> orderNoList) {
        log.info("订单回传订单号: {}", orderNoList);
        List<OrderItemShippingRecord> shippingRecordList = iOrderItemShippingRecordService.queryListByOrderNoList(orderNoList);
        // 渠道订单号集合
        List<String> channelOrderNoList = new ArrayList<>();
        // 多渠道tracking信息集合
        List<TrackingCallBackVo> trackingCallBackVoList = new ArrayList<>();

        if (CollUtil.isNotEmpty(shippingRecordList)) {
            for (OrderItemShippingRecord shippingRecord : shippingRecordList) {
                String orderNo = shippingRecord.getOrderNo();
                String shippingNo = shippingRecord.getShippingNo();

                String supplierTenantId = shippingRecord.getSupplierTenantId();

                if (shippingRecord.getSystemManaged() == null) {
                    shippingRecord.setSystemManaged(true);
                }
                Orders order = iOrdersService.getByOrderNo(orderNo);
                try {
                    ThebizarkDelegate delegate = initDelegate(supplierTenantId);
                    Result<OutOrder> result;
                    try {
                        result = delegate.orderApi().getOne(orderNo);
                    }catch (ThebizarkException thebizarkException){
                        log.info("使用orderNo获取订单信息出现异常,{}",thebizarkException.getMessage());
                        log.info("使用shippingNo获取订单信息,{} ",shippingNo);
                        result = delegate.orderApi().getOne(shippingNo);
                    }
                    OutOrder outOrder = result.getData();
                    if (outOrder != null) {
                        String errorCode = outOrder.getErrorCode();
                        // errorCode为空，或者为unexception，此情况为无异常，需要走后续操作
                        if (StringUtils.isBlank(errorCode) || StringUtils.equals(errorCode, "unexception")) {
                            String orderStatusEnum = outOrder.getOrderStatusEnum();
                            String warehouseCode = outOrder.getWarehouseCode();

                            Warehouse warehouse = iWarehouseService.queryByWarehouseType(supplierTenantId, WarehouseTypeEnum.BizArk, warehouseCode);
                            String warehouseSystemCode = warehouseCode;
                            if (warehouse != null) {
                                warehouseSystemCode = warehouse.getWarehouseSystemCode();
                            }
                            if (StringUtils.isBlank(orderStatusEnum)) {
                                // 恒健仓库订单状态为空，跳过不处理
                                continue;
                            }
                                List<OutShipment> shipmentPackages = outOrder.getShipmentPackages();
                                if (shipmentPackages != null) {
                                    // 多渠道回调信息
                                    TrackingCallBackVo trackingCallBackVo = new TrackingCallBackVo();
                                    if(StringUtils.isNotEmpty(order.getChannelOrderNo()) ){
                                        if(null != order.getChannelId()){
                                            if(null == order.getTrackingFlag() || order.getTrackingFlag().equals(1)){
                                                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.selectByIdNotTenant(order.getChannelId());
                                                List<String> channelNames = Arrays.asList("FineJoys Home", "MyDepot Outlet", "Mydepot3-US","Tik Tok-MyDepot4店-US");
                                                boolean channelNameFlag = channelNames.contains(tenantSalesChannel.getChannelName());

                                                boolean channelFlag = StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr())
                                                    && null != tenantSalesChannel.getState()
                                                    && tenantSalesChannel.getState().equals(1)
                                                    || channelNameFlag;
                                                if(null != tenantSalesChannel && channelFlag){
                                                    if(StringUtils.isNotEmpty(order.getChannelOrderNo()) && !channelOrderNoList.contains(order.getChannelOrderNo())){
                                                        trackingCallBackVo.setOrderNum(order.getChannelOrderNo()).setChannelId(tenantSalesChannel.getChannelName()).setShipWarehouse(warehouseSystemCode);
                                                        trackingCallBackVoList.add(trackingCallBackVo);
                                                        channelOrderNoList.add(order.getChannelOrderNo());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                        } else {
                            // insufficientInventoryException库存不足异常
                            // 发生任何形式的异常，都要保持定时器运行
                            shippingRecord.setShippingErrorCode(errorCode);
                            shippingRecord.setSystemManaged(true);
                        }
                    }
                } catch (Exception e) {
                    shippingRecord.setShippingErrorCode(e.getMessage());
                    log.error("恒健仓库【查询发货单】 发货单号{}，发生未知错误 {}", shippingNo, e.getMessage(), e);
                }
            }
            log.info("订单tracking信息回传头信息: {}",trackingCallBackVoList);
            if(CollectionUtil.isNotEmpty(trackingCallBackVoList)){
                List<String> orderNums = trackingCallBackVoList.stream()
                                                               .map(TrackingCallBackVo::getOrderNum)
                                                               .collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(orderNums)){
                    List<Orders> channelOrderNoIn = iOrdersService.findByChannelOrderNoIn(orderNums);
                    List<OrderItemTrackingRecord> listByOrderNo = new ArrayList<>();
                    List<com.zsmall.order.entity.domain.OrderItem> orderItemList = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn)){
                        List<String> orderNos = channelOrderNoIn.stream()
                                                                .map(Orders::getOrderNo)
                                                                .collect(Collectors.toList());
                        listByOrderNo = iOrderItemTrackingRecordService.getListByOrderNo(orderNos);
                        orderItemList = iOrderItemService.getList(orderNos);
                    }
                    // 没有tracking信息的订单号
                    List<String> exceptionOrderNumberList = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(channelOrderNoIn) && CollectionUtil.isNotEmpty(listByOrderNo)){
                    loop2: for(TrackingCallBackVo trackingCallBackVo : trackingCallBackVoList){
                            String shipDate = null;
                            List<TrackingCallBackPackagesVo> packages = new ArrayList<>();
                            loop: for(Orders orders : channelOrderNoIn){
                                if(trackingCallBackVo.getOrderNum().equals(orders.getChannelOrderNo())){
                                    for(OrderItemTrackingRecord orderItemTrackingRecord : listByOrderNo){
                                        if(orders.getOrderNo().equals(orderItemTrackingRecord.getOrderNo())){
                                            TrackingCallBackPackagesVo trackingCallBackPackagesVo = new TrackingCallBackPackagesVo();
                                            // trackingNo为空或者渠道商为空的排除
                                            if(StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsCarrier()) || StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsTrackingNo())){
                                                exceptionOrderNumberList.add(trackingCallBackVo.getOrderNum());
                                                continue loop2;
                                            }
                                            trackingCallBackPackagesVo.setCarrier(orderItemTrackingRecord.getLogisticsCarrier()).setTrackingNo(orderItemTrackingRecord.getLogisticsTrackingNo());
                                            if(null != orderItemTrackingRecord.getDispatchedTime()){
                                                String shipDate1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderItemTrackingRecord.getDispatchedTime());
                                                trackingCallBackPackagesVo.setShipDate(shipDate1);
                                                if(null == shipDate){
                                                    shipDate = shipDate1;
                                                }
                                            }
                                            TrackingCallBackPackagesItemsVo trackingCallBackPackagesItemsVo = new TrackingCallBackPackagesItemsVo();
                                            // 设置包裹重量和sellerSku
                                            for(com.zsmall.order.entity.domain.OrderItem orderItem : orderItemList){
                                                if(orders.getOrderNo().equals(orderItem.getOrderNo())){
                                                    ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItem.getProductSkuCode());
                                                    if(null != productSkuDetail){
                                                        switch (productSkuDetail.getPackWeightUnit()){
                                                            case lb:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.poundsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case kg:
                                                                trackingCallBackPackagesVo.setPackageWeight(productSkuDetail.getPackWeight());
                                                                break;
                                                            case g:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.gramsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                            case t:
                                                                trackingCallBackPackagesVo.setPackageWeight(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight()));
                                                                break;
                                                        }
                                                    }
                                                    trackingCallBackPackagesItemsVo.setSellerSku(orderItem.getChannelSku());
                                                    break;
                                                }
                                            }
                                            trackingCallBackPackagesItemsVo.setLineNum(orders.getLineOrderItemId()).setShipedQuantity(orderItemTrackingRecord.getQuantity());
                                            trackingCallBackPackagesVo.setItems(Arrays.asList(trackingCallBackPackagesItemsVo));
                                            packages.add(trackingCallBackPackagesVo);
                                            continue loop;
                                        }
                                    }
                                }
                            }
                            trackingCallBackVo.setPackages(packages);
                            trackingCallBackVo.setShipDate(shipDate);
                        }
                        // 去除没有tracking信息的订单
                        if(CollUtil.isNotEmpty(exceptionOrderNumberList)){
                            Iterator<TrackingCallBackVo> iterator = trackingCallBackVoList.iterator();
                            while (iterator.hasNext()) {
                                TrackingCallBackVo element = iterator.next();
                                if (exceptionOrderNumberList.contains(element.getOrderNum())) {
                                    iterator.remove();
                                }
                            }
                        }
                        try {
                            log.info("订单回传tracking信息至多渠道的全部tracking信息: {}",trackingCallBackVoList);
                            Integer batchSize = 1000;
                            Iterator<TrackingCallBackVo> iterator = trackingCallBackVoList.iterator();
                            List<TrackingCallBackVo> batch = new ArrayList<>(batchSize);
                            while (iterator.hasNext()) {
                                TrackingCallBackVo trackingCallBackVo = iterator.next();
                                batch.add(trackingCallBackVo);
                                if (batch.size() == batchSize) {
                                    log.info("订单回传tracking信息至多渠道,tracking信息: {}",batch);
                                    String str = JSON.toJSONString(batch);
                                    String messageId = IdUtil.fastSimpleUUID();
                                    Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                    rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,message);
                                    List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                     .collect(Collectors.toList());
                                    if(CollUtil.isNotEmpty(orderNumList)){
                                        iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                    }
                                    // 清空列表
                                    batch.clear();
                                }
                            }
                            // 剩余的
                            if (!batch.isEmpty()) {
                                log.info("订单回传tracking信息至多渠道,tracking信息: {}",batch);
                                String str = JSON.toJSONString(batch);
                                String messageId = IdUtil.fastSimpleUUID();
                                Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY, message);
                                List<String> orderNumList = batch.stream().map(TrackingCallBackVo::getOrderNum)
                                                                 .collect(Collectors.toList());
                                if(CollUtil.isNotEmpty(orderNumList)){
                                    iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList,2);
                                }
                                // 清空批次列表
                                batch.clear();
                            }
                        }catch (Exception e){
                            log.info("订单回传tracking信息至多渠道,tracking信息: {}, 出现异常: {}",trackingCallBackVoList,e.getMessage());
                        }
                    }
                }
            }
        }
    }

    @Override
    public void cancelOrderV2(String orderExtendId, String supplierTenantId) {
        try {
            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            Result result = delegate.orderApi().cancelOrderV2(orderExtendId);
        } catch (Exception e) {
            log.error("恒健仓库【取消发货单】 发生异常 {}", e.getMessage(), e);
            throw new ThebizarkException(500, "取消恒健仓库发货单[" + orderExtendId + "]出现未知错误");
        }
    }

    /**
     * 将恒健仓库物流信息转成TrackingRecord
     */
    private OrderItemTrackingRecord convertTrackingRecord(OutShipment shipmentPackage,
                                                          OrderItemShippingRecord shippingOrder,
                                                          OrderItem orderItem, OrderItemProductSku orderItemProductSku,
                                                          String warehouseSystemCode, ShippingStateEnum shippingState) {
        String erpSku = shipmentPackage.getErpSku();
        String carrier = shipmentPackage.getCarrier();
        String trackingNo = shipmentPackage.getTrackingNo();
        Integer shipedQuantity = shipmentPackage.getShipedQuantity();
        DateTime shipmentDate = shipmentPackage.getShipmentDate();

        // 承运商判断，转大写后存在FED的为FedEx，存在UPS的为UPS
        String newCarrier = carrier;
        if (StrUtil.contains(carrier.toUpperCase(), "FED")) {
            newCarrier = "FedEx";
        } else if (StrUtil.contains(carrier.toUpperCase(), "UPS")) {
            newCarrier = "UPS";
            //Added by Buddy @2024-10-19 增加OnTrac物流方式（erp新增OnTrac）分销进行适配
        } else if (StrUtil.contains(carrier.toUpperCase(), "ONTRAC")) {
            newCarrier = "OnTrac";
        }

        log.info("queryBizarkTacking - carrier = {}, newCarrier = {}", carrier, newCarrier);
        OrderItemTrackingRecord newTracking = new OrderItemTrackingRecord();
        newTracking.setShippingNo(shippingOrder.getShippingNo());
        newTracking.setLogisticsCarrier(newCarrier);
        newTracking.setLogisticsTrackingNo(trackingNo);
        newTracking.setQuantity(shipedQuantity);
        newTracking.setOrderNo(shippingOrder.getOrderNo());
        newTracking.setOrderItemNo(shippingOrder.getOrderItemNo());
        newTracking.setSku(orderItemProductSku.getSku());
        newTracking.setWarehouseCode(warehouseSystemCode);
        newTracking.setWarehouseSystemCode(warehouseSystemCode);
        newTracking.setLogisticsService(shipmentPackage.getCarrier());
        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);

        // 已发货，进行相关操作
        if (ObjectUtil.equals(shippingState, ShippingStateEnum.Shipped)) {
            // 已发货且发货时间不为空，直接取用恒健仓库的发货时间
            if (shipmentDate != null) {
                //将UTC时间转为北京时间
                shipmentDate =DateUtil.offsetHour(shipmentDate,8);
                newTracking.setDispatchedTime(shipmentDate);
                orderItem.setDispatchedTime(shipmentDate);
            } else {  // 已发货但发货时间为空，取用当前系统时间
                newTracking.setDispatchedTime(DateTime.now());
                orderItem.setDispatchedTime(DateTime.now());
            }
            // 子订单改为已发货
            orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
            // 已发货，查询一次物流信息
//            ZSMallOrderEventUtils.queryLogistics(newTracking);
        }
        return newTracking;
    }

}
