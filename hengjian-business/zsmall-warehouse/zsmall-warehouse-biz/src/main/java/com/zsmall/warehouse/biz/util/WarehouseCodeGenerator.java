package com.zsmall.warehouse.biz.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.service.AbstractCodeGenerator;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 仓库系统编号生成器实现类
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WarehouseCodeGenerator extends AbstractCodeGenerator {

    private final IWarehouseService iWarehouseService;
    private final ILogisticsTemplateService iLogisticsTemplateService;

    /**
     * 编号生成器
     *
     * @param type 主类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException {
        return codeGenerate(type, "");
    }

    /**
     * 编号生成器
     *
     * @param type    主类型
     * @param subType 子类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type, String subType) throws RStatusCodeException {
        log.info("编号生成器 type = {}, subType = {}", type);
        var code = "";
        var value = type.getValue();
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.WarehouseSystemCode.equals(type)) {
                code = subType + RandomUtil.randomNumbers(6);
                repeat = iWarehouseService.existsWarehouseSystemCode(code);
            } else if (BusinessCodeEnum.LogisticsTemplateCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = iLogisticsTemplateService.existsLogisticsTemplateCode(code);
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
            }
        }
        if (StrUtil.isNotBlank(code)) {
            return code;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
        }
    }
}
