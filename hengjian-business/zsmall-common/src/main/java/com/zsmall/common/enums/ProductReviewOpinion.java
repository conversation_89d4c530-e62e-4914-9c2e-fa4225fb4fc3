package com.zsmall.common.enums;

import java.util.Objects;

/**
 * 商品审核意见选项
 */
public enum ProductReviewOpinion {

    /**
     *
     */
    reason1("reason1", "Product image quality is too low.", "商品图片质量太低。"),

    /**
     *
     */
    reason2("reason2", "Product description is unclear.", "商品描述不清楚。"),

    /**
     *
     */
    reason3("reason3", "Product is illegal.", "商品不合法。"),

    /**
     *
     */
    reason4("reason4", "Product price is unreasonable.", "商品价格不合理。"),

    /**
     *
     */
    nullValue("nullValue", "", ""),

    ;

    private String code;
    private String enValue;
    private String zhValue;

    public String getCode() {
        return code;
    }

    public String getZhValue() {
        return zhValue;
    }

    public String getEnValue() {
        return enValue;
    }

    ProductReviewOpinion(String code, String enValue, String zhValue) {
        this.code = code;
        this.enValue = enValue;
        this.zhValue = zhValue;
    }

    public static ProductReviewOpinion fromCode(String code) {
        for (ProductReviewOpinion state : ProductReviewOpinion.values()) {
            if (Objects.equals(code, state.getCode())) {
                return state;
            }
        }
        return nullValue;
    }

}
