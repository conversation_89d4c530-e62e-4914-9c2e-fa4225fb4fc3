package com.zsmall.common.enums.productActivity;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <AUTHOR>
 * @date 2022-09-16
 **/
public enum ProductActivityStateEnum implements IEnum<String> {
    /**
     * 草稿
     */
    Draft,
    /**
     * 审核中
     */
    UnderReview,
    /**
     * 已发布
     */
    Published,
    /**
     * 进行中
     */
    InProgress,
    /**
     * 取消中
     */
    Canceling,
    /**
     * 未通过审核
     */
    NotApproved,
    /**
     * 已取消
     */
    Canceled,
    /**
     * 已售罄
     */
    SoldOut,
    /**
     * 已完成
     */
    Completed,

    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
