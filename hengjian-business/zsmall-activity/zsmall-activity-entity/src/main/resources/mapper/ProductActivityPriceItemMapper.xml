<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.ProductActivityPriceItemMapper">
    <select id="sumActivityDepositTotalPrice" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(papi.activity_deposit_total_price), 0.00)
        FROM product_activity_price_item papi
                 JOIN product_activity_item pai on papi.product_activity_item_id = pai.id WHERE pai.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(distributorId)">
            AND pai.tenant_id = #{distributorId}
        </if>
        <if test="actItemId != null">
            AND pai.id = #{actItemId}
        </if>
        <if test="activityCodeParent != null">
            AND papi.activity_code_parent = #{activityCodeParent}
        </if>
    </select>

    <select id="sumPlatformDepositTotalPrice" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(papi.platform_deposit_total_price), 0.00)
        FROM product_activity_price_item papi
                 JOIN product_activity_item pai on papi.product_activity_item_id = pai.id WHERE pai.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(distributorId)">
            AND pai.tenant_id = #{distributorId}
        </if>
        <if test="actItemId != null">
            AND pai.id = #{actItemId}
        </if>
        <if test="activityCodeParent != null">
            AND papi.activity_code_parent = #{activityCodeParent}
        </if>
    </select>

    <resultMap id="ActivitySimpleProductInfoVoMap" type="com.zsmall.activity.entity.domain.vo.productActivity.ActivitySimpleProductInfoVo">
        <result column="product_img" property="productImg"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="platform_pick_up_price" property="pickUpPrice"/>
        <result column="platform_drop_shipping_price" property="dropShippingPrice"/>
    </resultMap>
    <select id="querySimpleProductInfo" resultMap="ActivitySimpleProductInfoVoMap">
        SELECT pai.product_img,
               pai.product_name,
               pai.product_sku_code,
               papi.platform_pick_up_price,
               papi.platform_drop_shipping_price
        FROM product_activity_price_item papi
                 JOIN product_activity_item pai ON papi.product_activity_item_id = pai.id
        WHERE papi.activity_code = #{activityCode}
    </select>
</mapper>
