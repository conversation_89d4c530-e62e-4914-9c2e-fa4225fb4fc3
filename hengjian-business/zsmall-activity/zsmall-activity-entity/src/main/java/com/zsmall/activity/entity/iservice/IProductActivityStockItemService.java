package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.mapper.ProductActivityStockItemMapper;
import com.zsmall.activity.entity.domain.ProductActivityStockItem;

import java.util.List;

@Service
public class IProductActivityStockItemService extends ServiceImpl<ProductActivityStockItemMapper, ProductActivityStockItem> {

    @InMethodLog("根据商品活动子表（分销商）主键查询活动子库存")
    public List<ProductActivityStockItem> queryByActivityItemId(Long activityItemId) {
        LambdaQueryWrapper<ProductActivityStockItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductActivityStockItem::getProductActivityItemId, activityItemId);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog("根据商品活动子表（分销商）主键和仓库系统编号查询活动子库存")
    public ProductActivityStockItem queryByActivityItemIdAndWarehouse(Long activityItemId, String warehouseSystemCode) {
        LambdaQueryWrapper<ProductActivityStockItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductActivityStockItem::getProductActivityItemId, activityItemId);
        lqw.eq(ProductActivityStockItem::getWarehouseSystemCode, warehouseSystemCode);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    @InMethodLog("查询剩余数量大于零的库存")
    public List<ProductActivityStockItem> getEnoughByItemId(Long activityItemId, Integer quantity) {
        LambdaQueryWrapper<ProductActivityStockItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductActivityStockItem::getProductActivityItemId, activityItemId)
            .ge(ProductActivityStockItem::getQuantitySurplus, quantity);
        return this.list(queryWrapper);
    }

    @InMethodLog("根据子活动编号查询库存所在国家")
    public List<String> queryCountryByActivityCode(String activityCode) {
        return TenantHelper.ignore(() -> baseMapper.queryCountryByActivityCode(activityCode));
    }

    @InMethodLog("查询库存充足的活动子库存")
    public List<ProductActivityStockItem> queryAdequateStockByParams(String destCountry, Long activityItemId, Integer adjustQuantity, LogisticsTypeEnum logisticsType, Boolean logisticsAccount) {
        return baseMapper.queryAdequateStockByParams(destCountry, activityItemId, adjustQuantity, logisticsType.name(), logisticsAccount);
    }

    @InMethodLog("根据仓库系统编号统计有效的库存（且活动未结束的）")
    public Long countValidByWarehouseSystemCode(String warehouseSystemCode) {
        return baseMapper.countValidByWarehouseSystemCode(warehouseSystemCode);
    }
}
