package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.ChinaSpotProductSku;
import com.zsmall.activity.entity.mapper.ChinaSpotProductSkuMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【china_spot_product_sku(国内现货商品SKU表)】的数据库操作Service实现
* @createDate 2023-08-15 16:40:48
*/
@Service
public class IChinaSpotProductSkuService extends ServiceImpl<ChinaSpotProductSkuMapper, ChinaSpotProductSku> {


    @InMethodLog(value = "国内现货商品Id查询")
    public List<ChinaSpotProductSku> queryByChinaSpotProductId(Long chinaSpotProductId) {
        return TenantHelper.ignore(() -> lambdaQuery().eq(ChinaSpotProductSku::getChinaSpotProductId, chinaSpotProductId).list() , TenantType.Manager);
    }

    @InMethodLog(value = "Count有效的sku数量（相同Sku）")
    public Long countBySku(String sku) {
        return TenantHelper.ignore(() -> baseMapper.countBySku(sku) , TenantType.Manager);
    }

    @InMethodLog("国内现货Sku商品编号是否存在")
    public Boolean existProductCode(String productCode) {
        LambdaQueryWrapper<ChinaSpotProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ChinaSpotProductSku::getProductSkuCode, productCode);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    @InMethodLog(value = "Count有效的sku数量（相同productSkuCode）")
    public Long countByProductSkuCode(String productSkuCode) {
        return TenantHelper.ignore(() -> baseMapper.countByProductSkuCode(productSkuCode) , TenantType.Manager);
    }


}




